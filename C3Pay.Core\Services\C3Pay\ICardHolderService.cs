﻿using C3Pay.Core.Models;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using System.Collections.Generic;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Services
{
    public interface ICardHolderService
    {
        public Task<ServiceResponse<CardHolder>> GetCardHolderByEmiratesId(string emiratesId);
        public Task<ServiceResponse<CardHolder>> GetCardHolderByCardNumber(string cardNumber);
        public Task<ServiceResponse<CardHolder>> GetCardHolderByCitizenId(string citizenId);
        public Task<ServiceResponse<BlackListedEntity>> IsCardBlocked(string cardSerialNumber);
        public Task<ServiceResponse> BlockCard(string cardSerialNumber);
        Task<ServiceResponse> BulkUpdateBranchId(BranchIdUpdateDto branchIdUpdate);
        public Task<ServiceResponse> UpdateReplacementCardDetails(ReplacementCardUpdateDto cardDetails);
        public Task<ServiceResponse<List<SearchResult>>> Search(SearchRequest searchRequest);
        public Task<ServiceResponse<string>> GetCardPin(string cardSerialNumber);
        public Task<ServiceResponse> SendCardPin(string cardSerialNumber);
        public Task<ServiceResponse<List<DocumentReadDto>>> GetKYCDocuments(string citizenId);
        public Task<ServiceResponse> SaveKYCDocuments(List<DocumentReadDto> request);
        Task<ServiceResponse> UpdateRenewalCardDetails(RenewalCardUpdateDto cardDetails);
        Task<ServiceResponse> SyncRenewalCardDetails(RenewalCardSyncDto cardDetails);
        Task<ServiceResponse> UpdateSurchargeFeeFlag(string ppsAccountNumber);
    }
}
