﻿using AutoMapper;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services
{
    public class CardHolderService : ICardHolderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IESMOWebService _esmoWebService;
        private readonly IPPSService _ppsService;
        private readonly IBlobStorageService _blobStorageService;
        private readonly IKYCService _kYCService;

        private readonly ILogger _logger;
        private readonly IMapper _mapper;

        private readonly string _tempKycPortalContainer = "kyc-portal-temp";
        private readonly List<int> exchangeHousesFirstDigits = new List<int>() { 4, 7 };

        public CardHolderService(IUnitOfWork unitOfWork,
            IESMOWebService esmoWebService,
            IPPSService ppsService,
            IBlobStorageService blobStorageService,
            IKYCService kYCService,

            ILogger<CardHolderService> logger,
            IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _esmoWebService = esmoWebService;
            _blobStorageService = blobStorageService;
            _kYCService = kYCService;

            _ppsService = ppsService;
            _logger = logger;
            _mapper = mapper;
        }

        [Obsolete]
        public async Task<ServiceResponse<CardHolder>> GetCardHolderByCardNumber(string cardNumber)
        {
            var cardHolderDetailsResult = await this._esmoWebService.GetCardHolderDetails(new GetCardHolderDetailsRequestDto()
            {
                CardNumber = cardNumber,
                SearchParameter = Enums.CardHolderDetailsSearchParameter.CardNumber
            });

            var cardHolderDetailsResponse = cardHolderDetailsResult.Data;

            if (cardHolderDetailsResponse == null)
            {
                return new ServiceResponse<CardHolder>(false, string.Format(ConstantParam.CardHolderNotFoundByCardNumber, cardNumber));
            }

            var cardHolderDetails = cardHolderDetailsResponse.CardHolder;

            var cardHolder = this._mapper.Map<CardHolder>(cardHolderDetails);

            return new ServiceResponse<CardHolder>(cardHolder);
        }

        public async Task<ServiceResponse<CardHolder>> GetCardHolderByCitizenId(string citizenId)
        {
            var cardHolderDetailsResult = await this._esmoWebService.GetCardHolderDetailsV2(new GetCardHolderDetailsRequestDto()
            {
                CitizenId = citizenId,
                SearchParameter = Enums.CardHolderDetailsSearchParameter.CitizenId
            });

            var cardHolderDetailsResponse = cardHolderDetailsResult.Data;

            if (cardHolderDetailsResponse == null)
            {
                return new ServiceResponse<CardHolder>(false, cardHolderDetailsResult.ErrorMessage);
            }

            var cardHolderDetails = cardHolderDetailsResponse.CardHolder;

            var cardHolder = this._mapper.Map<CardHolder>(cardHolderDetails);

            return new ServiceResponse<CardHolder>(cardHolder);
        }

        public async Task<ServiceResponse<CardHolder>> GetCardHolderByEmiratesId(string emiratesId)
        {
            var cardHolder = await this._unitOfWork.CardHolders.FirstOrDefaultAsync(record => record.EmiratesId == emiratesId);

            if (cardHolder == null)
            {
                return new ServiceResponse<CardHolder>(false, string.Format(ConstantParam.CardHolderNotFoundByEmiratesId, emiratesId));
            }
            else
            {
                return new ServiceResponse<CardHolder>(cardHolder);
            }
        }

        public async Task<ServiceResponse> UpdateReplacementCardDetails(ReplacementCardUpdateDto cardDetails)
        {
            if (cardDetails != null)
            {
                var cardholder = await _unitOfWork.CardHolders.FirstOrDefaultAsync(record => record.C3RegistrationId == cardDetails.C3EmpRegId);
                if (cardholder == null)
                {
                    return new ServiceResponse<CardHolder>(false, string.Format(ConstantParam.CardHolderNotFoundByCitizenId, cardDetails.CitizenId));
                }

                cardholder.CardNumber = cardDetails.CardNumber.Remove(5, 6).Insert(6, "******");
                cardholder.CardSerialNumber = cardDetails.CardSerialNUmber;

                await _unitOfWork.CommitAsync();
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdateRenewalCardDetails(RenewalCardUpdateDto cardDetails) 
        {
            if (cardDetails != null)
            {
                var cardholder = await _unitOfWork.CardHolders.FirstOrDefaultAsync(record => record.C3RegistrationId == cardDetails.C3EmpRegId);
                if (cardholder == null)
                    return new ServiceResponse<CardHolder>(false, string.Format(ConstantParam.CardHolderNotFoundByCitizenId, cardDetails.CitizenId));
                cardholder.CardNumber = cardDetails.NewCardNumber.Remove(5, 6).Insert(6, "******");
                cardholder.CardSerialNumber = cardDetails.NewCardserialNumber;
                await _unitOfWork.CommitAsync();
            }
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> BulkUpdateBranchId(BranchIdUpdateDto branchIdUpdate)
        {
            var branchId = branchIdUpdate.BranchId;

            var employeeCount = branchIdUpdate.C3RegistrationIds.Count;

          
            var cardHolders = await this._unitOfWork.CardHolders.FindAsync(record => branchIdUpdate.C3RegistrationIds.Contains(record.C3RegistrationId), false);

            var foundCardHoldersCount = cardHolders.Count;

        
            foreach (var cardHolder in cardHolders)
            {
                var oldBranchId = cardHolder.CorporateId;

                cardHolder.CorporateId = branchIdUpdate.BranchId;

            }

            await this._unitOfWork.CommitAsync();


            return new ServiceResponse();
        }

        public async Task<ServiceResponse> BlockCard(string cardSerialNumber)
        {
            cardSerialNumber = cardSerialNumber.Trim();

            var blackListedEntity = await this._unitOfWork.BlackListedEntities.FirstOrDefaultAsync(b => b.Identifier == cardSerialNumber);

            if (blackListedEntity == null)
            {
                await _unitOfWork.BlackListedEntities.AddAsync(new BlackListedEntity()
                {
                    EntityType = BlackListedEntityType.CardSerialNumber,
                    Identifier = cardSerialNumber,
                    LastFailedAttemptTimeStamp = DateTime.Now,
                    IsActive = true
                });
            }
            else
            {
                blackListedEntity.IsActive = true;
                blackListedEntity.LastFailedAttemptTimeStamp = DateTime.Now;
            }

            await _unitOfWork.CommitAsync();
            return new ServiceResponse();
        }

        public async Task<ServiceResponse<BlackListedEntity>> IsCardBlocked(string cardSerialNumber)
        {
            cardSerialNumber = cardSerialNumber.Trim();

            Expression<Func<BlackListedEntity, bool>> blockedCardCondition = entity => !entity.IsDeleted
            && entity.Identifier.Trim() == cardSerialNumber && entity.EntityType == BlackListedEntityType.CardSerialNumber
            && entity.IsActive == true;

            var blocked = await _unitOfWork.BlackListedEntities.FirstOrDefaultAsync(blockedCardCondition);
            return new ServiceResponse<BlackListedEntity>(blocked);
        }

        public async Task<ServiceResponse<List<SearchResult>>> Search(SearchRequest searchRequest)
        {
            if (!string.IsNullOrEmpty(searchRequest.CorporateId) && exchangeHousesFirstDigits.Contains(int.Parse(searchRequest.CorporateId[..1])))
            {
                return new ServiceResponse<List<SearchResult>>(new List<SearchResult>());
            }

            var searchResult = await _esmoWebService.Search(searchRequest);

            if (!searchResult.IsSuccessful)
            {
                return new ServiceResponse<List<SearchResult>>(false, searchResult.ErrorMessage);
            }

            var cardHolders = searchResult.Data;

            if (cardHolders.Count > 0)
            {
                var citizenIds = cardHolders.Select(x => x.CitizenId).ToList();

                var registeredCardHoldersIds = (await _unitOfWork.Users.FindAsync(x => citizenIds.Contains(x.CardHolderId) && !x.IsDeleted))?.Select(x => x.CardHolderId).ToList();

                if (registeredCardHoldersIds != null && registeredCardHoldersIds.Count > 0)
                {
                    cardHolders = cardHolders.Where(ch => !registeredCardHoldersIds.Contains(ch.CitizenId)).ToList();
                }
            }

            return new ServiceResponse<List<SearchResult>>(cardHolders);
        }

        public async Task<ServiceResponse<string>> GetCardPin(string cardSerialNumber)
        {
            var cardPin = await _esmoWebService.GetCardPin(new GetCardPinRequest { CardSerialNumber = cardSerialNumber });
            if (!cardPin.IsSuccessful)
                return new ServiceResponse<string>(false, cardPin.ErrorMessage);

            return new ServiceResponse<string>(cardPin.Data.Pin);
        }

        public async Task<ServiceResponse> SendCardPin(string cardSerialNumber)
        {
            var cardPin = await _ppsService.SendCardPin(new SendCardPinRequest { CardSerialNumber = cardSerialNumber });
            if (!cardPin.IsSuccessful)
                return new ServiceResponse(false, cardPin.ErrorMessage);

            return cardPin;
        }

        public async Task<ServiceResponse<List<DocumentReadDto>>> GetKYCDocuments(string citizenId)
        {
            return await _kYCService.GetKYCDocuments(citizenId);
        }

        public async Task<ServiceResponse> SaveKYCDocuments(List<DocumentReadDto> request)
        {
            var kycDocuments = new List<DocumentSaveDto>();
            foreach (var item in request)
            {
                await _blobStorageService.UploadContentBlobAsync(item.DocumentBase64, item.DocumentName, _tempKycPortalContainer);
                var getReadUrl = await _blobStorageService.GetBlobReadURLAsync(item.DocumentName, _tempKycPortalContainer);

                var document = _mapper.Map<DocumentSaveDto>(item);
                document.DocumentUrl = getReadUrl.Data;

                kycDocuments.Add(document);
            }

            var result = await _kYCService.SaveKYCDocuments(kycDocuments);
            return result;
        }

        public async Task<ServiceResponse> SyncRenewalCardDetails(RenewalCardSyncDto cardDetails)
        {
            if (cardDetails != null)
            {
                var cardholder = await _unitOfWork.CardHolders.FirstOrDefaultAsync(record => record.C3RegistrationId == cardDetails.C3EmpRegId);
                if (cardholder == null)
                    return new ServiceResponse<CardHolder>(false, string.Format(ConstantParam.CardHolderNotFoundByCitizenId, cardDetails.CitizenId));
                cardholder.CardNumber = cardDetails.NewCardNumber.Remove(5, 6).Insert(6, "******");
                cardholder.CardSerialNumber = cardDetails.NewCardserialNumber;
                await _unitOfWork.CommitAsync();
            }
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdateSurchargeFeeFlag(string ppsAccountNumber)
        {
            try
            {
                _logger.LogInformation($"UpdateSurchargeFeeFlag: Updating surcharge fee flag for PpsAccountNumber: {ppsAccountNumber}");

                if (string.IsNullOrEmpty(ppsAccountNumber))
                {
                    _logger.LogError("UpdateSurchargeFeeFlag: PpsAccountNumber is null or empty");
                    return new ServiceResponse(false, "PpsAccountNumber cannot be null or empty");
                }

                // Find CardHolder by PpsAccountNumber
                var cardHolder = await _unitOfWork.CardHolders.FirstOrDefaultAsync(c => c.PpsAccountNumber == ppsAccountNumber);

                if (cardHolder == null)
                {
                    _logger.LogWarning($"UpdateSurchargeFeeFlag: CardHolder not found for PpsAccountNumber: {ppsAccountNumber}");
                    return new ServiceResponse(false, $"CardHolder not found for PpsAccountNumber: {ppsAccountNumber}");
                }

                // Update HasSurchargeFee flag if not already set
                if (!cardHolder.HasSurchargeFee)
                {
                    cardHolder.HasSurchargeFee = true;
                    await _unitOfWork.CommitAsync();

                    _logger.LogInformation($"UpdateSurchargeFeeFlag: Successfully updated HasSurchargeFee to true for CardHolder ID: {cardHolder.Id}, PpsAccountNumber: {ppsAccountNumber}");
                    return new ServiceResponse(true, "Surcharge fee flag updated successfully");
                }
                else
                {
                    _logger.LogDebug($"UpdateSurchargeFeeFlag: HasSurchargeFee already set to true for CardHolder ID: {cardHolder.Id}");
                    return new ServiceResponse(true, "Surcharge fee flag was already set to true");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"UpdateSurchargeFeeFlag: Error updating surcharge fee flag for PpsAccountNumber: {ppsAccountNumber}");
                return new ServiceResponse(false, $"Error updating surcharge fee flag: {ex.Message}");
            }
        }
    }
}
