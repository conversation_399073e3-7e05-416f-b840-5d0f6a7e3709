﻿using Azure.Messaging.ServiceBus;
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Messages.C3PayPlus;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Core.Services.C3Pay.Mock;
using C3Pay.Core.Services.C3Pay.UnEmpInsurance;
using C3Pay.Services.Handlers.MoneyTransfer.BankSearch.Handlers;
using C3Pay.Services.IntegrationEvents.In;
using C3Pay.Services.Membership;
using C3Pay.Services.Membership.Commands;
using C3Pay.Services.Membership.VPN.Commands;
using C3Pay.Services.RmtKyc;
using DocumentFormat.OpenXml.Wordprocessing;
using Edenred.Common.Core;
using MediatR;
using Microsoft.Azure.ServiceBus;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.WebJob
{
    /// <summary>
    /// 
    /// </summary>
    public class Functions
    {
        private readonly IMoneyTransferBeneficiaryService _beneficiaryService;
        private readonly IMoneyTransferBeneficiaryService _beneficiaryMockService;
        private readonly IMoneyTransferService _moneyTransferService;
        private readonly IMoneyTransferMockService _moneyTransferMockService;
        private readonly IMobileRechargeService _mobileRechargeService;
        private readonly ISftpProcessorService _sftpProcessorService;
        private readonly IKYCService _kycService;
        private readonly IESMOWebService _esmoWebService;
        private readonly IConfiguration _configuration;
        private readonly ICardHolderService _cardholderService;
        private readonly IUserService _userService;
        private readonly IReferralProgramService _referralProgramService;
        private readonly SynchronizeBanksAndBranchesHandler _synchronizeBanksAndBranchesHandler;
        private readonly IEHMoneyTransferService _exchangeHouseMoneyTransferService;
        private readonly IMessagingQueueService _messagingQueueService;
        private readonly IBillPaymentProcessingService _billPaymentProcessingService;
        private readonly IBillPaymentProcessingMockService _billPaymentProcessingMockService;
        private readonly ISpendPolicyService _spendPolicyService;
        private readonly IUnEmpInsuranceService _unEmpInsuranceService;
        private readonly ISubscriptionService _subscriptionService;
        private readonly IPortalUserService _portalUserService;
        private readonly IAuditTrailService _auditTrailService;
        private readonly IFeatureManager _featureManager;
        private readonly IRMTProfileService _rmtProfileService;
        private readonly IKycBlockExclusionService _kycBlockExclusionService;
        private readonly IMediator _mediator;
        private readonly IKycUnblockService _kycUnblockService;
        private readonly IRmtKycRefinementService _rmtKycRefinmentService;
        private readonly string _executionEnabledKey = "WebJobExecutionEnabled";

        /// <summary>
        /// Functions
        /// </summary>
        /// <param name="config"></param>
        /// <param name="beneficiaryService"></param>
        /// <param name="moneyTransferService"></param>
        /// <param name="mobileRechargeService"></param>
        /// <param name="sftpProcessorService"></param>
        /// <param name="messagingQueueService"></param>
        /// <param name="kycService"></param>
        /// <param name="esmoWebService"></param>
        /// <param name="cardHolderService"></param>
        /// <param name="beneficiaryMockService"></param>
        /// <param name="moneyTransferMockService"></param>
        /// <param name="userService"></param>
        /// <param name="referralProgramService"></param>
        /// <param name="billPaymentProcessingService"></param>
        /// <param name="billPaymentProcessingMockService"></param>
        /// <param name="synchronizeBanksAndBranchesHandler"></param>
        /// <param name="spendPolicyService"></param>
        /// <param name="exchangeHouseMoneyTransferService"></param>
        /// <param name="unEmpInsuranceService"></param>
        /// <param name="subscriptionService"></param>
        /// <param name="portalUserService"></param>
        /// <param name="auditTrailService"></param>
        /// <param name="featureManager"></param>
        /// <param name="rmtProfileService"></param>
        /// <param name="kycBlockExclusionService"></param>
        /// <param name="mediator"></param>
        /// <param name="kycUnblockService"></param>
        /// <param name="c3PayPlusMembershipAutomatedCallService"></param>
        /// <param name="rewardService"></param>
        public Functions(IConfiguration config,
            IMoneyTransferBeneficiaryService beneficiaryService,
            IMoneyTransferService moneyTransferService,
            IMobileRechargeService mobileRechargeService,
            ISftpProcessorService sftpProcessorService,
            IMessagingQueueService messagingQueueService,
            IKYCService kycService,
            IESMOWebService esmoWebService,
            ICardHolderService cardHolderService,
            IMoneyTransferBeneficiaryService beneficiaryMockService,
            IMoneyTransferMockService moneyTransferMockService,
            IUserService userService,
            IReferralProgramService referralProgramService,
            IBillPaymentProcessingService billPaymentProcessingService,
            IBillPaymentProcessingMockService billPaymentProcessingMockService,
            SynchronizeBanksAndBranchesHandler synchronizeBanksAndBranchesHandler,
            ISpendPolicyService spendPolicyService,
            IEHMoneyTransferService exchangeHouseMoneyTransferService,
            IUnEmpInsuranceService unEmpInsuranceService,
            ISubscriptionService subscriptionService,
            IPortalUserService portalUserService,
            IAuditTrailService auditTrailService,
            IFeatureManager featureManager,
            IRMTProfileService rmtProfileService,
            IKycBlockExclusionService kycBlockExclusionService,
            IMediator mediator,
            IKycUnblockService kycUnblockService,
            IRmtKycRefinementService rmtKycRefinmentService)
        {
            _configuration = config;
            _beneficiaryService = beneficiaryService;
            _moneyTransferService = moneyTransferService;
            _mobileRechargeService = mobileRechargeService;
            _sftpProcessorService = sftpProcessorService;
            _messagingQueueService = messagingQueueService;
            _kycService = kycService;
            _esmoWebService = esmoWebService;
            _beneficiaryMockService = beneficiaryMockService;
            _moneyTransferMockService = moneyTransferMockService;
            _cardholderService = cardHolderService;
            _userService = userService;
            _referralProgramService = referralProgramService;
            _billPaymentProcessingService = billPaymentProcessingService;
            _billPaymentProcessingMockService = billPaymentProcessingMockService;
            _synchronizeBanksAndBranchesHandler = synchronizeBanksAndBranchesHandler;
            _spendPolicyService = spendPolicyService;
            _exchangeHouseMoneyTransferService = exchangeHouseMoneyTransferService;
            _unEmpInsuranceService = unEmpInsuranceService;
            _subscriptionService = subscriptionService;
            _portalUserService = portalUserService;
            _auditTrailService = auditTrailService;
            _featureManager = featureManager;
            _rmtProfileService = rmtProfileService;
            _kycBlockExclusionService = kycBlockExclusionService;
            _mediator = mediator;
            _kycUnblockService = kycUnblockService;
            _rmtKycRefinmentService = rmtKycRefinmentService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("ManageBeneficiary")]
        public async Task ManageBeneficiary([ServiceBusTrigger("%RAKService:BeneficiaryQueueName%", Connection = "RAKService:BeneficiaryQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();


                var manageBeneficiary = JsonConvert.DeserializeObject<BeneficiaryMessageDto>(messageBody);

                if (manageBeneficiary == null)
                {
                    logger.LogError($"ManageBeneficiary Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                if (manageBeneficiary.Action == BaseEnums.MessageAction.Create)
                    await _beneficiaryService.AddExternalBeneficiary(manageBeneficiary.Id);
                else if (manageBeneficiary.Action == BaseEnums.MessageAction.Delete)
                    await _beneficiaryService.DeleteExternalBeneficiary(manageBeneficiary.Id);
                else if (manageBeneficiary.Action == BaseEnums.MessageAction.CreateWu)
                    await _beneficiaryService.AddExternalProviderBeneficiary(manageBeneficiary.Id);

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in ManageBeneficiary Job: {ex}");
                logger.LogError(ex, $"Error occurred in ManageBeneficiary Job, Body ={messageBody}");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("SendTransfer")]
        public async Task SendTransfer([ServiceBusTrigger("%RAKService:MoneyTransferQueueName%", Connection = "RAKService:MoneyTransferQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();


                var moneyTransferTransaction = JsonConvert.DeserializeObject<MoneyTransferMessageDto>(messageBody);

                if (moneyTransferTransaction == null)
                {
                    logger.LogError($"SendTransfer Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                if (moneyTransferTransaction.Action == BaseEnums.MessageAction.Create)
                {
                    var transactionresponse = await _moneyTransferService.AddExternalTransaction(moneyTransferTransaction.Id);
                    if (transactionresponse.Data == BaseEnums.Status.PENDING)
                    {
                        Int32 scheduledEnqueueDelay = 60;
                        Int32.TryParse(this._configuration["RAKService:MessageProcessInDelay"], out scheduledEnqueueDelay);

                        //Reschedule message in the queue            
                        await _messagingQueueService.SendAsync<MoneyTransferMessageDto>(new MoneyTransferMessageDto { Id = moneyTransferTransaction.Id, Action = BaseEnums.MessageAction.Create },
                                                                                        this._configuration["RAKService:MoneyTransferQueueConnectionString"],
                                                                                        this._configuration["RAKService:MoneyTransferQueueName"],
                                                                                        scheduledEnqueueDelay);
                    }
                    else if (transactionresponse.Data == BaseEnums.Status.COMPLETED)
                    {
                        var scheduledEnqueueDelay = 1;
                        Int32.TryParse(this._configuration["RAKService:TransactionUpdateDelay"], out scheduledEnqueueDelay);

                        //Reschedule message in the queue            
                        await _messagingQueueService.SendAsync<MoneyTransferMessageDto>(new MoneyTransferMessageDto { Id = moneyTransferTransaction.Id, Action = BaseEnums.MessageAction.Update },
                                                                                        this._configuration["RAKService:MoneyTransferQueueConnectionString"],
                                                                                        this._configuration["RAKService:MoneyTransferQueueName"],
                                                                                        scheduledEnqueueDelay);
                    }
                }
                else if (moneyTransferTransaction.Action == BaseEnums.MessageAction.Update)
                {
                    var thisTransactionResponse = await _moneyTransferService.UpdateTransactionFromExternal(moneyTransferTransaction.Id, true);

                    if (thisTransactionResponse.IsSuccessful)
                    {
                        // Retrying status check for three times
                        var thisTransaction = thisTransactionResponse.Data;
                        if (thisTransaction.TransferType == BaseEnums.TransactionType.RAKMoney &&
                            thisTransaction.Status == BaseEnums.Status.PENDING &&
                            thisTransaction.StatusCheckRetryCount < 3 && !string.IsNullOrEmpty(thisTransaction.WUMsgId))
                        {
                            int scheduledEnqueueDelay;
                            switch (thisTransaction.StatusCheckRetryCount)
                            {
                                case 1:
                                    scheduledEnqueueDelay = 2;
                                    break;
                                case 2:
                                    scheduledEnqueueDelay = 8;
                                    break;
                                default:
                                    scheduledEnqueueDelay = 2;
                                    break;
                            }

                            //Reschedule message in the queue            
                            await _messagingQueueService.SendAsync<MoneyTransferMessageDto>(new MoneyTransferMessageDto { Id = moneyTransferTransaction.Id, Action = BaseEnums.MessageAction.Update },
                                                                                            this._configuration["RAKService:MoneyTransferQueueConnectionString"],
                                                                                            this._configuration["RAKService:MoneyTransferQueueName"],
                                                                                            scheduledEnqueueDelay);
                        }
                    }
                }

                logger.LogDebug($"SendTransfer Job: Message handled, Body = {messageBody}");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in SendTransfer Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in SendTransfer Job, Body = {messageBody}");
            }
        }


        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ProcessBankTransactionsReports")]
        [Singleton]
        public async Task ProcessBankTransactionsReports([TimerTrigger("%RAKService:ProcessBankTransactionsReportsSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }

            try
            {
                logger.LogDebug($"{nameof(ProcessBankTransactionsReports)} Job :: Job started...");

                await this._sftpProcessorService.ProcessTransactionsReports();
                logger.LogDebug($"{nameof(ProcessBankTransactionsReports)} Job :: Job completed.");

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ProcessBankTransactionsReports)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ProcessBankTransactionsReports)} Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("UpdatePendingBankTransactions")]
        [Singleton]
        public async Task UpdatePendingBankTransactions([TimerTrigger("%RAKService:UpdatePendingBankTransactionsSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(UpdatePendingBankTransactions)} Job: Job started...");

                var schedulerIsEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableUpdatePendingStatusScheduler);
                if (schedulerIsEnabled)
                {
                    await this._moneyTransferService.UpdatePendingTransactions();
                }
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(UpdatePendingBankTransactions)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(UpdatePendingBankTransactions)} Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ReverseFailedMoneyTransferTransactions")]
        [Singleton]
        public async Task ReverseFailedMoneyTransferTransactions([TimerTrigger("%RAKService:ReverseFailedBankTransactionsSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }

            try
            {
                logger.LogDebug($"{nameof(ReverseFailedMoneyTransferTransactions)} Job: Job started...");

                await _moneyTransferService.ReverseFailedMoneyTransferTransactions();

                logger.LogDebug($"{nameof(ReverseFailedMoneyTransferTransactions)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ReverseFailedMoneyTransferTransactions)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ReverseFailedMoneyTransferTransactions)} Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ProcessRakStatement")]
        [Singleton]
        public async Task ProcessRakStatement([TimerTrigger("%RAKService:ProcessRakStatementSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }

            try
            {

                var schedulerIsEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableProcessRakStatementScheduler);
                if (schedulerIsEnabled)
                {
                    await _sftpProcessorService.ProcessRakStatement();
                }


            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ProcessRakStatement)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ProcessRakStatement)} Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ProcessRmtSubmission")]
        [Singleton]
        public async Task ProcessRmtSubmission([TimerTrigger("%RAKService:ProcessRmtSubmissionSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(ProcessRmtSubmission)} Job: Job started...");

                await _sftpProcessorService.ProcessRmtSubmissions();

                logger.LogDebug($"{nameof(ProcessRmtSubmission)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ProcessRmtSubmission)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ProcessRmtSubmission)} Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ProcessRmtAcknowledgement")]
        [Singleton]
        public async Task ProcessRmtAcknowledgement([TimerTrigger("%RAKService:ProcessRmtAcknowledgementSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(ProcessRmtAcknowledgement)} Job: Job started...");

                await _sftpProcessorService.ProcessRmtAcknowledgements();

                logger.LogDebug($"{nameof(ProcessRmtAcknowledgement)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ProcessRmtSubmission)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ProcessRmtAcknowledgement)} Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("UploadMissingProfilesImages")]
        [Singleton]
        public async Task UploadMissingProfilesImages([TimerTrigger("%RAKService:UploadMissingProfilesImagesSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                var uploadMissingProfilesImagesIsEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableUploadingMissingProfilesAttachments);

                if (uploadMissingProfilesImagesIsEnabled)
                {
                    await _rmtProfileService.UploadMissingProfilesImages();
                }
                else
                {
                    logger.LogWarning($"{nameof(UploadMissingProfilesImages)} Job is Off!");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error occurred in {nameof(UploadMissingProfilesImages)} Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("SynchronizeWithDingServiceData")]
        [Singleton]
        public async Task SynchronizeWithDingServiceData([TimerTrigger("%MobileRechargeService:SynchronizeWithDingSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                await _mobileRechargeService.SynchronizeDingCountryWithDB();

                await _mobileRechargeService.SynchronizeDingRegionWithDB();
                await _mobileRechargeService.SynchronizeDingProviderWithDB();
                await _mobileRechargeService.SynchronizeDingProviderStatusWithDB();
                await _mobileRechargeService.SynchronizeDingProductsWithDB();
                await _mobileRechargeService.SynchronizeDingProductsDescriptionWithDB();
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in SynchronizeWithDingServiceData Job: {ex.ToString()}");
                logger.LogError(ex, "Error occurred in SynchronizeWithDingServiceData Job");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("UpdateProvidersForMobileRechargeBeneficiary")]
        public async Task UpdateProvidersForMobileRechargeBeneficiary([ServiceBusTrigger("%MobileRechargeService:QueueName%", Connection = "MobileRechargeService:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = Encoding.UTF8.GetString(requestMessage.Body);


                var beneficiaryInfo = JsonConvert.DeserializeObject<MobileRechargeMessageDto>(messageBody);

                if (beneficiaryInfo == null)
                {
                    logger.LogError($"UpdateProvidersForMobileRechargeBeneficiary Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                if (beneficiaryInfo.Action == BaseEnums.MessageAction.Update)
                    await _mobileRechargeService.UpdateProvidersForMobileRechargeBeneficiary(beneficiaryInfo.Id);

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in UpdateProvidersForMobileRechargeBeneficiary Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in UpdateProvidersForMobileRechargeBeneficiary Job, Body ={messageBody}");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("UpdateMobileRechargeTransferStatus")]
        [Singleton]
        public async Task UpdateMobileRechargeTransferStatus([TimerTrigger("%MobileRechargeService:UpdateStatusSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"UpdateMobileRechargeTransferStatus  Job: Update Transfer Status process started at : {DateTime.Now.ToString("ddMMyyHmss")}");

                var isRatesExperimentEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableRatesExperiment);

                await _mobileRechargeService.UpdateStatusForPendingTransactions(isRatesExperimentEnabled);

                logger.LogDebug($"UpdateMobileRechargeTransferStatus  Job: Update Transfer Status process finished at : {DateTime.Now.ToString("ddMMyyHmss")}");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in UpdateMobileRechargeTransferStatus Job: {ex.ToString()}");
                logger.LogError(ex, "Error occurred in UpdateMobileRechargeTransferStatus Job");
            }
        }

        [FunctionName("CrossCheckMobileRechargeBeneficiary")]
        public async Task CrossCheckMobileRechargeBeneficiary([ServiceBusTrigger("%MobileRechargeService:ServiceBusTopicName%", "%MobileRechargeService:ServiceBusSubscriptionName%", Connection = "MobileRechargeService:ServiceBusConnectionString")] ServiceBusReceivedMessage message, ILogger log)
        {
            var messageBody = Encoding.UTF8.GetString(message.Body);
            var messageId = message.MessageId;
            try
            {

                var serviceBusMessage = JsonConvert.DeserializeObject<ServiceBusTopicMessageDto>(messageBody);

                if (serviceBusMessage != null)
                {
                    if (string.Equals(serviceBusMessage.Type, "MRFailedTransactionEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var request = JsonConvert.DeserializeObject<MobileRechargeFailedTransactionMessageDto>(serviceBusMessage.Data);

                        var result = await _mobileRechargeService.CrossCheckBeneficiaryForFailedTransaction(request.TransactionId,
                                                                request.BeneficiaryId, request.BeneficiaryCountryCode, request.TransactionErrorContext);

                        if (!result.IsSuccessful)
                            log.LogWarning($"MsgID_{messageId}_{serviceBusMessage.Type}_CrossCheckFailedWithError_{result.ErrorMessage}");
                    }
                }
                else
                    log.LogWarning($"MsgID_{messageId}_CrossCheckBeneficiary Service Bus Invalid message");
            }
            catch (Exception ex)
            {
                log.LogError($"MsgID_{messageId}_CrossCheckBeneficiary Service Bus Exception : {ex}");
                return;
            }
        }

        [FunctionName("ProcessMobileRechargeRenewals")]
        [Singleton]
        public async Task ProcessMobileRechargeRenewals([TimerTrigger("%MobileRechargeService:RenewalSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }

            try
            {
                var isAuotRenewalEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewal);
                if (isAuotRenewalEnabled)
                {
                    var isTargetedRenewalDiscountEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewalTargetedDiscount);
                    await _mobileRechargeService.ProcessScheduledRenewals(isTargetedRenewalDiscountEnabled);
                }

            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error occured in ProcessMobileRechargeRenewals Job: {ex.ToString()}");
                logger.LogError(ex, "Error occured in ProcessMobileRechargeRenewals Job");
            }
        }



        [FunctionName("ProcessMobileRechargeLowBalanceRenewals")]
        [Singleton]
        public async Task ProcessMobileRechargeLowBalanceRenewals([TimerTrigger("%MobileRechargeService:LowBalanceRenewalSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }

            try
            {
                var retryDueToLowBalanceEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRecharge_RetryDueToLowBalance);
                if (retryDueToLowBalanceEnabled)
                {
                    await _mobileRechargeService.ProcessLowBalanceScheduledRenewals();

                }

            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error occured in ProcessMobileRechargeRenewals Job: {ex.ToString()}");
                logger.LogError(ex, "Error occured in ProcessMobileRechargeRenewals Job");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("CompleteUserRegistration")]
        public async Task CompleteUserRegistration([ServiceBusTrigger("%ESMOService:QueueName%", Connection = "ESMOService:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;

            try
            {
                messageBody = requestMessage.Body.ToString();

                var completeUserRegistrationMessage = JsonConvert.DeserializeObject<CompleteUserRegistrationDto>(messageBody);

                if (completeUserRegistrationMessage == null)
                {
                    logger.LogWarning($"CompleteUserRegistration Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                #region MarkCardHolderAsAppRegistered

                var updateAppStatusResult = await _esmoWebService.MarkCardHolderAsAppRegistered(new MarkCardHolderAsAppRegisteredRequestDto()
                {
                    AppStatus = completeUserRegistrationMessage.AppStatus,
                    CardSerialNo = completeUserRegistrationMessage.CardSerialNo,
                    CorporateId = completeUserRegistrationMessage.CorporateId
                });

                if (!updateAppStatusResult.IsSuccessful)
                {
                    logger.LogWarning($"CompleteUserRegistration-UpdateAppStatus Job: Failed, Reason: {updateAppStatusResult.ErrorMessage}, Body = {messageBody}");
                    return;
                }

                #endregion

                #region UpdatePhoneNumber

                if (completeUserRegistrationMessage.IsInternationalPhoneNumberUser != true)
                {
                    var updatePhoneNumberResult = await _kycService.UpdatePhoneNumber(new UpdatePhoneNumberRequestDto()
                    {
                        CitizenId = completeUserRegistrationMessage.CitizenId,
                        Mobile = completeUserRegistrationMessage.Mobile
                    });

                    if (!updatePhoneNumberResult.IsSuccessful)
                    {
                        logger.LogWarning($"CompleteUserRegistration-UpdatePhoneNumber Job: Failed, Reason: {updatePhoneNumberResult.ErrorMessage}, Body = {messageBody}");
                        return;
                    }
                }


                #endregion

                #region Set Spend Policy Status

                if (completeUserRegistrationMessage.IsInternationalPhoneNumberUser != true)
                {
                    var tryGetUser = await this._userService.GetUserByPhoneNumber(completeUserRegistrationMessage.Mobile);

                    if (!tryGetUser.IsSuccessful)
                    {
                        logger.LogWarning($"CompleteUserRegistration-PopulateSpendPolicyStatus Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                    }
                    else
                    {
                        var user = tryGetUser.Data;

                        var tryPopulateSpendPolicyStatus = await this._spendPolicyService.SetSpendPolicy(user, true);

                        if (!tryPopulateSpendPolicyStatus.IsSuccessful)
                        {
                            logger.LogWarning($"CompleteUserRegistration-PopulateSpendPolicyStatus Job: Failed, Reason: {tryPopulateSpendPolicyStatus.ErrorMessage}, Body = {messageBody}");
                        }
                    }

                }

                #endregion

                logger.LogDebug($"CompleteUserRegistration Job: Message handled, Body = {messageBody}");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in CompleteUserRegistration Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in CompleteUserRegistration Job, Body = {messageBody}");
            }
        }

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="requestMessage"></param>
        ///// <param name="logger"></param>
        ///// <param name="cancellationToken"></param>
        ///// <returns></returns>
        //[FunctionName("UpdateRegistrationStatus")]
        //public async Task UpdateRegistrationStatus([ServiceBusTrigger("%ESMOService:QueueName%", Connection = "ESMOService:QueueConnectionString")] ServiceBusReceivedMessage  requestMessage, ILogger logger, CancellationToken cancellationToken)
        //{
        //    string messageBody = string.Empty;

        //    try
        //    {
        //        messageBody = requestMessage.Body.ToString();

        //        logger.LogInformation($"UpdateRegistrationStatus Job: Message received at {DateTime.UtcNow}, Body = {messageBody}");

        //        var updateAppStatusMessage = JsonConvert.DeserializeObject<MarkCardHolderAsAppRegisteredMessageDto>(messageBody);

        //        if (updateAppStatusMessage == null)
        //        {
        //            logger.LogWarning($"UpdateRegistrationStatus Job: Unknown Message received, Body = {messageBody}");
        //            return;
        //        }

        //        var updateAppStatusResult = await _esmoWebService.MarkCardHolderAsAppRegistered(new MarkCardHolderAsAppRegisteredRequestDto()
        //        {
        //            AppStatus = updateAppStatusMessage.AppStatus,
        //            CardSerialNo = updateAppStatusMessage.CardSerialNo,
        //            CorporateId = updateAppStatusMessage.CorporateId
        //        });

        //        if (!updateAppStatusResult.IsSuccessful)
        //        {
        //            logger.LogWarning($"UpdateRegistrationStatus Job: Failed, Reason: {updateAppStatusResult.ErrorMessage}, Body = {messageBody}");
        //            return;
        //        }

        //        logger.LogInformation($"UpdateRegistrationStatus Job: Message handled, Body = {messageBody}");
        //    }
        //    catch (Exception ex)
        //    {
        //        await Console.Error.WriteLineAsync($"Error occurred in UpdateRegistrationStatus Job: {ex.ToString()}");
        //        logger.LogError(ex, $"Error occurred in UpdateRegistrationStatus Job, Body = {messageBody}");
        //    }
        //}

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("RefreshRates")]
        [Singleton]
        public async Task RefreshRates([TimerTrigger("%RAKService:RefreshRatesSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("RefreshRates Job: Refresh Rates Started");

                await this._moneyTransferService.RefreshRates();

                logger.LogDebug("RefreshRates Job: Refresh Rates Completed");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in RefreshRates Job: {ex.ToString()}");
                logger.LogError(ex, "Error occurred in RefreshRates Job");
            }


        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ProcessRMTCreationFile")]
        [Singleton]
        public async Task ProcessRMTCreationFile([TimerTrigger("%MoneyTransferService:RmtCreationSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("ProcessRMTCreationFile Job: RMT Creation File process started");

                await _sftpProcessorService.ReadUpdateRMTCreationFileAsync();

                logger.LogDebug("ProcessRMTCreationFile Job: RMT Creation File process completed");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error occured in ProcessRMTCreationFile Job: {ex.ToString()}");
                logger.LogError(ex, "Error occured in ProcessRMTCreationFile Job");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ValidateEmiratesIdExpiry")]
        [Singleton]
        public async Task ValidateEmiratesIdExpiry([TimerTrigger("%KycExpiry:ValidateEmiratesIdExpiryScheduler%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("ValidateEmiratesIdExpiry :: Started");

                var canValidate = await _featureManager.IsEnabledAsync(FeatureFlags.KycValidateEmiratesIdExpiry);
                if (canValidate)
                {
                    await _userService.ValidateUpdationKYCExpiry();
                }


            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occured in ValidateEmiratesIdExpiry");
            }
        }

        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ValidateAdditionKYCExpiry")]
        [Singleton]
        public async Task ValidateAdditionKYCExpiry([TimerTrigger("%KycExpiry:ValidateAdditionKYCScheduler%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }

            try
            {
                logger.LogDebug("ValidateAdditionKYC :: Started");

                var canValidate = await _featureManager.IsEnabledAsync(FeatureFlags.KycValidateEmiratesIdExpiry);
                if (canValidate)
                {
                    await _userService.ValidateAdditionKYCExpiry();
                }


                logger.LogDebug("ValidateAdditionKYC :: Completed");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occured in ValidateAdditionKYC");
            }
        }


        /// <summary>
        /// /
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("FreeTransferExpiry")]
        [Singleton]
        public async Task FreeTransferExpiry([TimerTrigger("%MoneyTransferService:FreeTransferExpiryScheduler%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                var enabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableFreeTransferExpiry);
                if (enabled)
                {
                    await _moneyTransferService.SendFreeTransferExpiryNotification();
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occured in FreeTransferExpiry scheduler");
            }
        }

        /// <summary>
        /// Retrying beneficiaries scheduler
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("RetryBeneficiary")]
        [Singleton]
        public async Task RetryBeneficiary([TimerTrigger("%MoneyTransferService:RetryBeneficiarySchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("RetryBeneficiary Job: Retry beneficiary Started");

                await _beneficiaryService.RetryPendingBeneficiaries();

                logger.LogDebug("RetryBeneficiary Job: Retry beneficiary Completed");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in RetryBeneficiary Job: {ex.ToString()}");
                logger.LogError(ex, "Error occurred in RetryBeneficiary Job");
            }
        }

        #region Mock Services 

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("ManageMockBeneficiary")]
        public async Task ManageMockBeneficiary([ServiceBusTrigger("%MoneyTransferService:BeneficiaryMockQueueName%", Connection = "MoneyTransferService:BeneficiaryMockQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();

                var manageBeneficiary = JsonConvert.DeserializeObject<BeneficiaryMessageDto>(messageBody);

                if (manageBeneficiary == null)
                {
                    logger.LogError($"ManageMockBeneficiary Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                if (manageBeneficiary.Action == BaseEnums.MessageAction.Create)
                    await _beneficiaryMockService.AddExternalBeneficiary(manageBeneficiary.Id);
                else if (manageBeneficiary.Action == BaseEnums.MessageAction.Delete)
                    await _beneficiaryMockService.DeleteExternalBeneficiary(manageBeneficiary.Id);

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in ManageMockBeneficiary Job: {ex}");
                logger.LogError(ex, $"Error occurred in ManageMockBeneficiary Job, Body ={messageBody}");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("SendMockTransfer")]
        public async Task SendMockTransfer([ServiceBusTrigger("%MoneyTransferService:MoneyTransferMockQueueName%", Connection = "MoneyTransferService:MoneyTransferMockQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();


                var moneyTransferTransaction = JsonConvert.DeserializeObject<MoneyTransferMessageDto>(messageBody);

                if (moneyTransferTransaction == null)
                {
                    logger.LogError($"SendMockTransfer Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                if (moneyTransferTransaction.Action == BaseEnums.MessageAction.Create)
                {
                    var transactionresponse = await _moneyTransferMockService.AddExternalTransaction(moneyTransferTransaction.Id);
                    if (transactionresponse.Data == BaseEnums.Status.PENDING)
                    {
                        Int32 scheduledEnqueueDelay = 60;
                        Int32.TryParse(this._configuration["RAKService:MessageProcessInDelay"], out scheduledEnqueueDelay);

                        //Reschedule message in the queue            
                        await _messagingQueueService.SendAsync<MoneyTransferMessageDto>(new MoneyTransferMessageDto { Id = moneyTransferTransaction.Id, Action = BaseEnums.MessageAction.Create },
                                                                                        this._configuration["MoneyTransferService:MoneyTransferMockQueueConnectionString"],
                                                                                        this._configuration["MoneyTransferService:MoneyTransferMockQueueName"],
                                                                                        scheduledEnqueueDelay);
                    }
                }

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in SendMockTransfer Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in SendMockTransfer Job, Body = {messageBody}");
            }
        }

        #endregion

        #region Card Replacement Update
        /// <summary>
        /// 
        /// </summary>
        [FunctionName("ReplacementCardUpdate")]
        public async Task CardReplacementQueueMessages(
        [ServiceBusTrigger("%ReplacementCardUpdateService:QueueName%", Connection = "ReplacementCardUpdateService:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();
                var replacementcardUpdateMessage = JsonConvert.DeserializeObject<ReplacementCardUpdateDto>(messageBody);

                if (replacementcardUpdateMessage == null)
                {
                    logger.LogWarning($"ReplacementcardUpdate Job: Unknown Message received, Body = {messageBody}");
                    return;
                }
                await _cardholderService.UpdateReplacementCardDetails(replacementcardUpdateMessage);

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in ReplacementcardUpdate Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in ReplacementcardUpdate Job, Body = {messageBody}");
            }
        }


        [FunctionName("RenewalCardUpdate")]
        public async Task CardReplacementTopicMessages([ServiceBusTrigger("%RenewalCardUpdate:ServiceBusTopicName%", "%RenewalCardUpdate:ServiceBusSubscriptionName%", Connection = "RenewalCardUpdate:ServiceBusConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger log)
        {

            var messageBody = requestMessage.Body.ToString();
            var messageId = requestMessage.MessageId;

            if (string.Equals(requestMessage.Subject, "RenewalCardActivated", StringComparison.OrdinalIgnoreCase))
            {
                try
                {
                    messageBody = requestMessage.Body.ToString();
                    var cardUpdateMessage = JsonConvert.DeserializeObject<RenewalCardUpdateDto>(messageBody);
                    if (cardUpdateMessage == null || string.IsNullOrEmpty(cardUpdateMessage.NewCardserialNumber)
                        || string.IsNullOrEmpty(cardUpdateMessage.NewCardNumber))
                    {
                        log.LogWarning($"RenewalCardUpdate Job: Unknown Message received, Body = {messageBody}");
                        return;
                    }
                    await _cardholderService.UpdateRenewalCardDetails(cardUpdateMessage);

                }
                catch (Exception ex)
                {
                    await Console.Error.WriteLineAsync($"Error occurred in ReplacementcardUpdate Job: {ex.ToString()}");
                    log.LogError(ex, $"Error occurred in ReplacementcardUpdate Job, Body = {messageBody}");
                }
            }
            else if (string.Equals(requestMessage.Subject, "RenewalCardSyncEvent", StringComparison.OrdinalIgnoreCase))
            {
                try
                {
                    messageBody = requestMessage.Body.ToString();
                    var cardSyncMessage = JsonConvert.DeserializeObject<RenewalCardSyncDto>(messageBody);
                    if (cardSyncMessage == null || string.IsNullOrEmpty(cardSyncMessage.NewCardserialNumber)
                        || string.IsNullOrEmpty(cardSyncMessage.NewCardNumber))
                    {
                        log.LogWarning($"RenewalCardSync Job: Unknown Message received, Body = {messageBody}");
                        return;
                    }
                    await _cardholderService.SyncRenewalCardDetails(cardSyncMessage);

                }
                catch (Exception ex)
                {
                    await Console.Error.WriteLineAsync($"Error occurred in RenewalCardSync Job: {ex.ToString()}");
                    log.LogError(ex, $"Error occurred in RenewalCardSync Job, Body = {messageBody}");
                }
            }
            else
            {
                log.LogWarning($"RenewalCardUpdate Job: Unknown Message received, Body = {messageBody}");
                return;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        [FunctionName("DeletedCardUpdate")]
        public async Task DeletedCardQueueMessages(
        [ServiceBusTrigger("%DeletedCardUpdateService:QueueName%", Connection = "DeletedCardUpdateService:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();


                var deletedCardUpdateMessage = JsonConvert.DeserializeObject<DeletedCardUpdateDto>(messageBody);
                if (deletedCardUpdateMessage == null)
                {
                    logger.LogWarning($"DeletedcardUpdate Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                var enableCardDeletion = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableDeletedCardUpdate);
                if (enableCardDeletion)
                {
                    var user = await _userService.GetUserByCitizenIdForAllApplications(deletedCardUpdateMessage.CitizenId);
                    if (!user.IsSuccessful)
                    {
                        logger.LogWarning($"DeletedcardUpdate Job: Get User :: {user.ErrorMessage}");
                        return;
                    }

                    var result = await _userService.RemoveUserAccount(user.Data.Id, deactivateRmt: true, setCardHolderAsDeleted: true);
                    if (!result.IsSuccessful)
                    {
                        logger.LogWarning($"DeletedcardUpdate Job: RemoveUserAccount :: {result.ErrorMessage}");
                        return;
                    }
                }

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in DeletedCardUpdate Job: {ex}");
                logger.LogError(ex, $"Error occurred in DeletedCardUpdate Job, Body = {messageBody}");
            }
        }
        #endregion

        #region Branch Id Update

        /// <summary>
        /// 
        /// </summary>
        [FunctionName("BulkBranchIdUpdate")]
        public async Task BranchIdUpdate(
        [ServiceBusTrigger("%BranchIdUpdateService:QueueName%", Connection = "BranchIdUpdateService:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();

                var branchIdUpdateMessage = JsonConvert.DeserializeObject<BranchIdUpdateDto>(messageBody);

                if (branchIdUpdateMessage == null)
                {
                    logger.LogWarning($"BranchIdUpdate Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                await _cardholderService.BulkUpdateBranchId(branchIdUpdateMessage);

            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Error occurred in BranchIdUpdate Job, Body = {messageBody}");
            }
        }

        #endregion

        /// <summary>
        /// 
        /// </summary>
        [FunctionName("ProcessReferralProgramReward")]
        public async Task ProcessReferralProgramReward(
        [ServiceBusTrigger("%ReferralProgramService:QueueName%", Connection = "ReferralProgramService:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();

                var referralProgramRewardMessage = JsonConvert.DeserializeObject<RewardUserForReferralDto>(messageBody);

                if (referralProgramRewardMessage == null)
                {
                    logger.LogWarning($"ProcessReferralProgramReward Job: Unknown Message received, Body = {messageBody}");
                    return;
                }
                await _referralProgramService.RewardUser(referralProgramRewardMessage.ReferralCode);

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in ProcessReferralProgramReward Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in ProcessReferralProgramReward Job, Body = {messageBody}");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        [FunctionName("PushSalaryProcessedEvents")]
        public async Task PushSalaryProcessedEvents([ServiceBusTrigger("%SalaryProcessingEventService:QueueName%", Connection = "SalaryProcessingEventService:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {

            var isFeatureEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableSalaryEventProcessFromTopic);
            if (isFeatureEnabled)
            {
                logger.LogWarning("Job skipped due to feature flag setting on - MT_EnableSalaryEventProcessFromTopic");
                return; // Exit early if the topic is enabled.
            }

            string messageBody = string.Empty;

            try
            {
                messageBody = requestMessage.Body.ToString();

                var pushSalaryProcessedEventsMessage = JsonConvert.DeserializeObject<PushSalaryProcessedEventsDto>(messageBody);
                if (pushSalaryProcessedEventsMessage == null)
                {
                    logger.LogWarning($"PushSalaryProcessedEvents Job: Unknown Message received, Body = {messageBody}");
                    return;
                }


                await this._userService.ProcessUsersSalaryEvents(pushSalaryProcessedEventsMessage);

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in PushSalaryProcessedEvents Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in PushSalaryProcessedEvents Job, Body = {messageBody}");
            }
        }

        #region Direct Transfers
        [FunctionName("ClaimDirectMoneyTransfers")]
        public async Task ClaimDirectMoneyTransfers([ServiceBusTrigger("%MoneyTransferService:ClaimPendingDirectTransfersQueueName%", Connection = "MoneyTransferService:ClaimPendingDirectTransfersQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;

            try
            {
                logger.LogDebug("ClaimDirectMoneyTransfers Job:: Job started.");

                // Read incoming message.
                messageBody = requestMessage.Body.ToString();

                // Deserialize message.
                var pendingDirectTransfersDto = JsonConvert.DeserializeObject<ClaimPendingDirectTransfersDto>(messageBody);
                if (pendingDirectTransfersDto is null)
                {
                    logger.LogError($"ClaimDirectMoneyTransfers Job:: Unknown message received. Body = {messageBody}");
                    return;
                }

                await this._moneyTransferService.ClaimDirectMoneyTransfers(pendingDirectTransfersDto.AccountNumber);

                logger.LogDebug("ClaimDirectMoneyTransfers Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"ClaimDirectMoneyTransfers Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"ClaimDirectMoneyTransfers Job:: Error occurred. Body = {messageBody}");
            }
        }

        [FunctionName("ReversePendingDirectMoneyTransfers")]
        public async Task ReversePendingDirectMoneyTransfers([TimerTrigger("%MoneyTransferService:ReversePendingDirectMoneyTransfersSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            string messageBody = string.Empty;

            try
            {
                logger.LogDebug("ReversePendingDirectMoneyTransfers Job:: Job started.");

                await this._moneyTransferService.ReversePendingDirectMoneyTransfers();

                logger.LogDebug("ReversePendingDirectMoneyTransfers Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"ReversePendingDirectMoneyTransfers Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"ReversePendingDirectMoneyTransfers Job:: Error occurred. Body = {messageBody}");
            }
        }

        [FunctionName("ReverseFailedDirectMoneyTransfers")]
        public async Task ReverseFailedDirectMoneyTransfers([TimerTrigger("%MoneyTransferService:ReverseFailedDirectMoneyTransfersSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            string messageBody = string.Empty;

            try
            {
                logger.LogDebug("ReverseFailedDirectMoneyTransfers Job:: Job started.");

                await this._moneyTransferService.ReverseFailedDirectMoneyTransfers();

                logger.LogDebug("ReverseFailedDirectMoneyTransfers Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"ReverseFailedDirectMoneyTransfers Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"ReverseFailedDirectMoneyTransfers Job:: Error occurred. Body = {messageBody}");
            }
        }

        [FunctionName("ReverseOnHoldTransactions")]
        public async Task ReverseOnHoldTransactions([TimerTrigger("%MoneyTransferService:ReverseOnHoldSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            string messageBody = string.Empty;

            try
            {
                logger.LogDebug("ReverseOnHoldTransactions Job:: Job started.");

                await this._moneyTransferService.ReverseOnHoldTransactions();

                logger.LogDebug("ReverseOnHoldTransactions Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"ReverseOnHoldTransactions Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"ReverseOnHoldTransactions Job:: Error occurred. Body = {messageBody}");
            }
        }

        #endregion

        #region Bill Payments Function  
        /// <summary>
        /// Synchronize BillPayment Providers
        /// </summary>
        /// <param name="timerInfo">0 */12 * * * (Run  every 12 Hours)</param>
        /// <param name="logger"></param>
        /// <returns></returns>
        [FunctionName("SynchronizeBillPaymentProviders")]
        [Singleton]
        public async Task
           SynchronizeBillPaymentProviders([TimerTrigger("%PaykiiService:RefreshDataIntervalCronExpression%",
            RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("SynchronizeBillPaymentProviders Job:: Job started.");

                await this._billPaymentProcessingService.SyncData();

                logger.LogDebug("SynchronizeBillPaymentProviders Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"SynchronizeBillPaymentProviders Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"SynchronizeBillPaymentProviders Job:: Error occurred.");
            }
        }

        [FunctionName("SynchronizeBillPaymentFxRates")]
        [Singleton]
        public async Task
        SynchronizeBillPaymentFxRates([TimerTrigger("%BillPayment:FxRateRefreshIntervalCronExpression%",
            RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("SynchronizeBillPaymentFxRates Job:: Job started.");

                await this._billPaymentProcessingService.SynchronizeBillPaymentFxRates();

                logger.LogDebug("SynchronizeBillPaymentFxRates Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"SynchronizeBillPaymentFxRates Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"SynchronizeBillPaymentFxRates Job:: Error occurred.");
            }
        }

        /// <summary>
        /// Fetch Bill Due
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("FetchBillDue")]
        public async Task FetchBillDue([ServiceBusTrigger("%BillPayment:AddBillerQueueName%", Connection = "BillPayment:AddBillerQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();


                var addBillRequest = JsonConvert.DeserializeObject<BillPaymentNewBillerMessageDto>(messageBody);

                if (addBillRequest == null)
                {
                    logger.LogError($"FetchBillDue Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                if (addBillRequest.Action == BaseEnums.MessageAction.Create)
                {
                    var transactionresponse = await _billPaymentProcessingService.FetchAmountDueForBiller(addBillRequest.Id);
                }

                logger.LogDebug($"FetchBillDue Job: Message handled, Body = {messageBody}");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in FetchBillDue Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in FetchBillDue Job, Body = {messageBody}");
            }
        }

        /// <summary>
        /// Process Pending Payment
        /// </summary>
        /// <param name="requestMessage"></param>
        /// <param name="logger"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [FunctionName("ProcessPendingPayment")]
        public async Task ProcessPendingPayment([ServiceBusTrigger("%BillPayment:ProcessPaymentQueueName%", Connection = "BillPayment:ProcessPaymentQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();

                logger.LogTrace($"ProcessPendingPayment - Message received at {DateTime.UtcNow}, Body = {messageBody}");

                var addBillRequest = JsonConvert.DeserializeObject<BillPaymentTransactionMessageDto>(messageBody);

                if (addBillRequest == null)
                {
                    logger.LogTrace($"ProcessPendingPayment - Unknown msg  received at {DateTime.UtcNow}, Body = {messageBody}");
                    return;
                }

                if (addBillRequest.Action == BaseEnums.MessageAction.Create)
                {
                    var transactionresponse = await _billPaymentProcessingService.ProcessQueuePayment(addBillRequest.Id, addBillRequest.ProductId, addBillRequest.InputAmount, addBillRequest.InputAmountCurrency);

                    if (!transactionresponse.IsSuccessful)
                    {
                        logger.LogTrace($"ProcessPendingPayment - Failed at {DateTime.UtcNow}, Body = {messageBody}");
                    }
                }
                logger.LogDebug($"ProcessPendingPayment -  Message handled at {DateTime.UtcNow}");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in ProcessPendingPayment Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in ProcessPendingPayment Job, Body = {messageBody}");
            }
        }

        [FunctionName("CheckForAmountDueExpiration")]
        [Singleton]
        public async Task
              CheckForAmountDueExpiration([TimerTrigger("%BillPayment:AmountDueExpireIntervalCronExpression%",
                RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("CheckForAmountDueExpiration Job:: Job started.");

                await this._billPaymentProcessingService.CheckForAmountDueExpiration();

                logger.LogDebug("CheckForAmountDueExpiration Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"CheckForAmountDueExpiration Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"CheckForAmountDueExpiration Job:: Error occurred.");
            }
        }

        #region Bill Payment Mocks 


        /// <summary>
        /// Fetch Bill Due
        /// </summary>
        ///// <param name="requestMessage"></param>
        ///// <param name="logger"></param>
        ///// <param name="cancellationToken"></param>
        /// <returns></returns>
        //[FunctionName("MockFetchBillDue")]
        //public async Task MockFetchBillDue([ServiceBusTrigger("%BillPayment:MockAddBillerQueueName%", Connection = "BillPayment:MockAddBillerQueueConnectionString")] ServiceBusReceivedMessage  requestMessage, ILogger logger, CancellationToken cancellationToken)
        //{
        //    string messageBody = string.Empty;
        //    try
        //    {
        //        messageBody = requestMessage.Body.ToString();

        //        logger.LogInformation($"Mock FetchBillDue Job: Message received at {DateTime.UtcNow}, Body = {messageBody}");

        //        var addBillRequest = JsonConvert.DeserializeObject<BillPaymentNewBillerMessageDto>(messageBody);

        //        if (addBillRequest == null)
        //        {
        //            logger.LogError($"Mock FetchBillDue Job: Unknown Message received, Body = {messageBody}");
        //            return;
        //        }

        //        if (addBillRequest.Action == BaseEnums.MessageAction.Create)
        //        {
        //            var transactionresponse = await _billPaymentProcessingMockService.FetchAmountDueForBiller(addBillRequest.Id);
        //        }

        //        logger.LogInformation($"Mock FetchBillDue Job: Message handled, Body = {messageBody}");
        //    }
        //    catch (Exception ex)
        //    {
        //        await Console.Error.WriteLineAsync($" Error occurred in Mock FetchBillDue Job: {ex.ToString()}");
        //        logger.LogError(ex, $"Error occurred in Mock FetchBillDue Job, Body = {messageBody}");
        //    }
        //}

        /// <summary>
        /// Process Pending Payment
        /// </summary>
        ///// <param name="requestMessage"></param>
        ///// <param name="logger"></param>
        ///// <param name="cancellationToken"></param>
        /// <returns></returns>
        //[FunctionName("MockProcessPendingPayment")]
        //public async Task MockProcessPendingPayment([ServiceBusTrigger("%BillPayment:MockProcessPaymentQueueName%", Connection = "BillPayment:MockProcessPaymentQueueConnectionString")] ServiceBusReceivedMessage  requestMessage, ILogger logger, CancellationToken cancellationToken)
        //{
        //    string messageBody = string.Empty;
        //    try
        //    {
        //        messageBody = requestMessage.Body.ToString();

        //        logger.LogTrace($"Mock ProcessPendingPayment - Message received at {DateTime.UtcNow}, Body = {messageBody}");

        //        var addBillRequest = JsonConvert.DeserializeObject<BillPaymentTransactionMessageDto>(messageBody);

        //        if (addBillRequest == null)
        //        {
        //            logger.LogTrace($"Mock ProcessPendingPayment - Unknown msg  received at {DateTime.UtcNow}, Body = {messageBody}");
        //            return;
        //        }

        //        if (addBillRequest.Action == BaseEnums.MessageAction.Create)
        //        {
        //            var transactionresponse = await _billPaymentProcessingMockService.ProcessQueuePayment(addBillRequest.Id, addBillRequest.ProductId, addBillRequest.InputAmount, addBillRequest.InputAmountCurrency);

        //            if (!transactionresponse.IsSuccessful)
        //            {
        //                logger.LogTrace($"Mock ProcessPendingPayment - Failed at {DateTime.UtcNow}, Body = {messageBody}");
        //            }
        //        }
        //        logger.LogTrace($"Mock ProcessPendingPayment -  Message handled at {DateTime.UtcNow}");
        //    }
        //    catch (Exception ex)
        //    {
        //        await Console.Error.WriteLineAsync($"Mock Error occurred in ProcessPendingPayment Job: {ex.ToString()}");
        //        logger.LogError(ex, $"Mock Error occurred in ProcessPendingPayment Job, Body = {messageBody}");
        //    }
        //}

        #endregion

        #endregion

        ///// <summary>
        ///// /
        ///// </summary>
        ///// <param name="timerInfo"></param>
        ///// <param name="logger"></param>
        //[FunctionName("SynchronizeBanksAndBranches")]
        //[Singleton]
        //public async Task SynchronizeBanksAndBranches([TimerTrigger("%MoneyTransferService:RefreshBanksAndBranchesSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        //{
        //    try
        //    {
        //        logger.LogInformation("SynchronizeBanksAndBranches Job: Process started");

        //        await _synchronizeBanksAndBranchesHandler.Handle(new SynchronizeBanksAndBranchesCommand());

        //        logger.LogInformation("SynchronizeBanksAndBranches Job: Process completed");
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.Error.WriteLine($"Error occured in SynchronizeBanksAndBranches Job: {ex.ToString()}");

        //        logger.LogError(ex, "Error occured in SynchronizeBanksAndBranches Job");
        //    }
        //}

        [FunctionName("ProcessEHMoneyTransfer")]
        public async Task ProcessEHMoneyTransfer([ServiceBusTrigger("%ExchangeHouseSettings:MoneyTransferQueueName%", Connection = "ExchangeHouseSettings:MoneyTransferQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;

            try
            {

                // Read incoming message.
                messageBody = requestMessage.Body.ToString();

                // Deserialize message.
                var moneyTransferTransaction = JsonConvert.DeserializeObject<MoneyTransferMessageDto>(messageBody);
                if (moneyTransferTransaction is null)
                {
                    logger.LogError($"ProcessEHMoneyTransfer Job:: Unknown message received. Body = {messageBody}");
                    return;
                }

                // Start processing transaction if action was 'Create'.
                if (moneyTransferTransaction.Action == BaseEnums.MessageAction.Create)
                {
                    await _exchangeHouseMoneyTransferService.ProcessTransaction(moneyTransferTransaction.Id);
                }

                logger.LogDebug("ProcessEHMoneyTransfer Job:: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"ProcessEHMoneyTransfer Job:: Error occurred. Exception: {ex}");
                logger.LogError(ex, $"ProcessEHMoneyTransfer Job:: Error occurred. Body = {messageBody}");
            }
        }


        #region UnEmployment Insurance
        [FunctionName("ProcessUnEmpSubscriptionCompleted")]
        public async Task ProcessUnEmpSubscriptionCompleted([ServiceBusTrigger("%UnEmploymentInsurance:ServiceBusTopicName%", "%UnEmploymentInsurance:ServiceBusSubscriptionName%", Connection = "UnEmploymentInsurance:ServiceBusConnectionString")] ServiceBusReceivedMessage message, ILogger log)
        {
            var messageBody = Encoding.UTF8.GetString(message.Body);
            var messageId = message.MessageId;
            try
            {

                var serviceBusMessage = JsonConvert.DeserializeObject<ServiceBusTopicMessageDto>(messageBody);

                if (serviceBusMessage != null)
                {
                    if (string.Equals(serviceBusMessage.Type, "SubscriptionCompletedEvent", StringComparison.OrdinalIgnoreCase)
                    || string.Equals(serviceBusMessage.Type, "AlreadySubscribedCompletedEvent", StringComparison.OrdinalIgnoreCase)
                    || string.Equals(serviceBusMessage.Type, "InstallmentPaidFromB2BEvent", StringComparison.OrdinalIgnoreCase)
                    || string.Equals(serviceBusMessage.Type, "InstallmentDiffersCompletedEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var subscriptionUpdatedResult = JsonConvert.DeserializeObject<UnEmploymentInsuranceCompletedMessageDto>(serviceBusMessage.Data);

                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(subscriptionUpdatedResult.CitizenId);
                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogWarning($"MsgID_{messageId}_{serviceBusMessage.Type}_CITIZENID_{subscriptionUpdatedResult.CitizenId} Job Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            var _unEmpInsurancePayment = new UnEmpInsurancePayment
                            {
                                PolicyNumber = subscriptionUpdatedResult.PolicyNumber,
                                PaidInstallments = subscriptionUpdatedResult.PaidInstallment,
                                TotalInstallments = subscriptionUpdatedResult.TotalInstallment,
                                InceptionDate = subscriptionUpdatedResult.InceptionDate,
                                ExpiryDate = subscriptionUpdatedResult.ExpiryDate,
                                NextDueDate = subscriptionUpdatedResult.NextDueDate,
                                NextDueAmount = subscriptionUpdatedResult.NextDueAmount,
                                IsSubscribedExternally = subscriptionUpdatedResult.IsSubscribedExternally,
                                IsAutoPayment = subscriptionUpdatedResult.IsAutoPayment
                            };
                            bool isInstallment = false;
                            if (string.Equals(serviceBusMessage.Type, "InstallmentPaidFromB2BEvent", StringComparison.OrdinalIgnoreCase) ||
                                string.Equals(serviceBusMessage.Type, "InstallmentDiffersCompletedEvent", StringComparison.OrdinalIgnoreCase))
                            {
                                isInstallment = true;
                            }
                            if (string.Equals(serviceBusMessage.Type, "AlreadySubscribedCompletedEvent", StringComparison.OrdinalIgnoreCase))
                            {
                                _unEmpInsurancePayment.IsSubscribedExternally = true;
                                _unEmpInsurancePayment.IsAutoPayment = false;
                            }
                            await _unEmpInsuranceService.UpdateProcessedInsurance(_unEmpInsurancePayment, tryGetUser.Data, isInstallment);

                        }
                    }
                    if (string.Equals(serviceBusMessage.Type, "SubscriptionErrorEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var subscriptionErrorResult = JsonConvert.DeserializeObject<UnEmpInsuranceErrorResponseDto>(serviceBusMessage.Data);

                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(subscriptionErrorResult.CitizenId);
                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogWarning($"MsgID_{messageId}_{serviceBusMessage.Type}_CITIZENID_{subscriptionErrorResult.CitizenId} Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            await _unEmpInsuranceService.UpdateFailedInsurance(subscriptionErrorResult.Error, tryGetUser.Data);
                        }
                    }
                    if (string.Equals(serviceBusMessage.Type, "NewSubscriptionFromB2BEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var subscriptionCreatedResult = JsonConvert.DeserializeObject<UnEmploymentInsuranceCompletedMessageDto>(serviceBusMessage.Data);
                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(subscriptionCreatedResult.CitizenId);
                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogWarning($"{serviceBusMessage.Type}_CITIZENID_{subscriptionCreatedResult.CitizenId} Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            // New Subscription from B2B Event 
                            if (string.Equals(serviceBusMessage.Type, "NewSubscriptionFromB2BEvent", StringComparison.OrdinalIgnoreCase))
                            {
                                var _unEmpInsurancePayment = new UnEmpInsurancePayment
                                {
                                    Amount = subscriptionCreatedResult.Amount,
                                    AmountCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.AmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.AmountCurrency,
                                    AmountVAT = subscriptionCreatedResult.VatAmount,
                                    AmountVATCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.VatAmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.VatAmountCurrency,
                                    Fee = subscriptionCreatedResult.FeeAmount,
                                    FeeCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.FeeAmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.FeeAmountCurrency,
                                    PolicyNumber = subscriptionCreatedResult.PolicyNumber,
                                    PaidInstallments = subscriptionCreatedResult.PaidInstallment,
                                    TotalInstallments = subscriptionCreatedResult.TotalInstallment,
                                    InceptionDate = subscriptionCreatedResult.InceptionDate,
                                    ExpiryDate = subscriptionCreatedResult.ExpiryDate,
                                    NextDueDate = subscriptionCreatedResult.NextDueDate,
                                    NextDueAmount = subscriptionCreatedResult.NextDueAmount,
                                    NextDueAmountCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.NextDueAmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.NextDueAmountCurrency,
                                    CreatedDate = DateTime.Now,
                                    DeletedBy = null,
                                    DeletedDate = null,
                                    IsAutoPayment = true,
                                    IsSubscribedExternally = subscriptionCreatedResult.IsSubscribedExternally,
                                    IsDeleted = false,
                                    OptionId = 0, // Need to set in service
                                    PaidDate = null,
                                    Remarks = string.Empty,
                                    Source = UnEmpInsuranceSource.Corporate,
                                    TotalAmount = subscriptionCreatedResult.TotalAmount,
                                    TotalAmountCurrency = subscriptionCreatedResult.TotalAmountCurrency,
                                    UpdatedDate = null,
                                    Status = UnEmpInsuranceStatus.Subscribed,
                                    UserId = tryGetUser.Data.Id,
                                    Category = string.IsNullOrEmpty(subscriptionCreatedResult.Category) ? UnEmpInsuranceCategory.A :
                                           (subscriptionCreatedResult.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B),
                                };

                                await _unEmpInsuranceService.CreateNewSubscriptionFromB2B(_unEmpInsurancePayment, tryGetUser.Data, subscriptionCreatedResult.PaymentOption, UnEmpInsurancePartnerType.DirectClient, subscriptionCreatedResult.IsSalaryDebit);
                            }
                        }
                    }
                    if (string.Equals(serviceBusMessage.Type, "SubscriptionCompletedAfterSalaryDebitEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var subscriptionCompletedAfterDebitResult = JsonConvert.DeserializeObject<UnEmploymentInsuranceCompletedMessageDto>(serviceBusMessage.Data);
                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(subscriptionCompletedAfterDebitResult.CitizenId);
                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogWarning($"MsgID_{messageId}_{serviceBusMessage.Type}_CITIZENID_{subscriptionCompletedAfterDebitResult.CitizenId} Job Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {
                            var _unEmpInsurancePayment = new UnEmpInsurancePayment
                            {
                                PolicyNumber = subscriptionCompletedAfterDebitResult.PolicyNumber,
                                PaidInstallments = subscriptionCompletedAfterDebitResult.PaidInstallment,
                                TotalInstallments = subscriptionCompletedAfterDebitResult.TotalInstallment,
                                InceptionDate = subscriptionCompletedAfterDebitResult.InceptionDate,
                                ExpiryDate = subscriptionCompletedAfterDebitResult.ExpiryDate,
                                NextDueDate = subscriptionCompletedAfterDebitResult.NextDueDate,
                                NextDueAmount = subscriptionCompletedAfterDebitResult.NextDueAmount,
                            };
                            await _unEmpInsuranceService.UpdateProcessedInsurance(_unEmpInsurancePayment, tryGetUser.Data, false, true);

                        }
                    }

                    //If for the last 3 months, the employees has no salary
                    if (string.Equals(serviceBusMessage.Type, "CancelSubscriptionEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var cancelSubscriptionsResult = JsonConvert.DeserializeObject<UnEmpInsuranceCancellationDto>(serviceBusMessage.Data);

                        var tryGetUsers = await _userService.GetUsersByCitizenIds(cancelSubscriptionsResult.CitizenIds.ToList());

                        if (!tryGetUsers.IsSuccessful)
                            log.LogWarning($"CancelSubscriptionEvent Job: Failed, Reason: {tryGetUsers.ErrorMessage}, Body = {messageBody}");
                        else
                            await _unEmpInsuranceService.CancelInsuranceSubscriptions(tryGetUsers.Data);
                    }

                    //If subscription request from B2B is in Queue
                    if (string.Equals(serviceBusMessage.Type, "NewSubscriptionFromB2BAddedInQueueEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var inlineRequest = JsonConvert.DeserializeObject<UnEmploymentInsuranceAddedInQueueMessageDto>(serviceBusMessage.Data);

                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(inlineRequest.CitizenId);

                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogWarning($"{serviceBusMessage.Type}_CITIZENID_{inlineRequest.CitizenId} Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            var _unEmpInsuranceWithPendingPayment = new UnEmpInsurancePayment
                            {
                                UserId = tryGetUser.Data.Id,
                                Amount = inlineRequest.Amount,
                                AmountCurrency = ConstantParam.DefaultCurrency,
                                AmountVAT = inlineRequest.VatAmount,
                                AmountVATCurrency = ConstantParam.DefaultCurrency,
                                Fee = inlineRequest.FeeAmount,
                                FeeCurrency = ConstantParam.DefaultCurrency,
                                TotalAmount = inlineRequest.TotalAmount,
                                TotalAmountCurrency = ConstantParam.DefaultCurrency,
                                Status = UnEmpInsuranceStatus.InProgress,
                                Remarks = inlineRequest.Remarks,
                                IsSubscribedExternally = false,
                                Source = UnEmpInsuranceSource.Corporate,
                                IsAutoPayment = inlineRequest.IsAutoPayment,
                                Category = inlineRequest.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B,
                            };
                            //Defaulted to Direct Client as B2B portal doesn't process insurance for ExhangeHouse clients
                            await _unEmpInsuranceService.AddNewInsuranceFromB2B(_unEmpInsuranceWithPendingPayment, UnEmpInsurancePartnerType.DirectClient);
                        }
                    }
                    if (string.Equals(serviceBusMessage.Type, "NewSubscriptionFromB2BCompletedEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var subscriptionCreatedResult = JsonConvert.DeserializeObject<UnEmploymentInsuranceCompletedMessageDto>(serviceBusMessage.Data);
                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(subscriptionCreatedResult.CitizenId);
                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogWarning($"{serviceBusMessage.Type}_CITIZENID_{subscriptionCreatedResult.CitizenId} Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            // New Subscription from B2B Event 
                            if (string.Equals(serviceBusMessage.Type, "NewSubscriptionFromB2BCompletedEvent", StringComparison.OrdinalIgnoreCase))
                            {
                                var _unEmpInsurancePayment = new UnEmpInsurancePayment
                                {
                                    Amount = subscriptionCreatedResult.Amount,
                                    AmountCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.AmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.AmountCurrency,
                                    AmountVAT = subscriptionCreatedResult.VatAmount,
                                    AmountVATCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.VatAmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.VatAmountCurrency,
                                    Fee = subscriptionCreatedResult.FeeAmount,
                                    FeeCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.FeeAmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.FeeAmountCurrency,
                                    PolicyNumber = subscriptionCreatedResult.PolicyNumber,
                                    PaidInstallments = subscriptionCreatedResult.PaidInstallment,
                                    TotalInstallments = subscriptionCreatedResult.TotalInstallment,
                                    InceptionDate = subscriptionCreatedResult.InceptionDate,
                                    ExpiryDate = subscriptionCreatedResult.ExpiryDate,
                                    NextDueDate = subscriptionCreatedResult.NextDueDate,
                                    NextDueAmount = subscriptionCreatedResult.NextDueAmount,
                                    NextDueAmountCurrency = string.IsNullOrEmpty(subscriptionCreatedResult.NextDueAmountCurrency) ? ConstantParam.DefaultCurrency : subscriptionCreatedResult.NextDueAmountCurrency,
                                    CreatedDate = DateTime.Now,
                                    DeletedBy = null,
                                    DeletedDate = null,
                                    IsAutoPayment = true,
                                    IsSubscribedExternally = subscriptionCreatedResult.IsSubscribedExternally,
                                    IsDeleted = false,
                                    OptionId = 0, // Need to set in service
                                    PaidDate = null,
                                    Remarks = string.Empty,
                                    Source = UnEmpInsuranceSource.Corporate,
                                    TotalAmount = subscriptionCreatedResult.TotalAmount,
                                    TotalAmountCurrency = subscriptionCreatedResult.TotalAmountCurrency,
                                    UpdatedDate = null,
                                    Status = UnEmpInsuranceStatus.Subscribed,
                                    UserId = tryGetUser.Data.Id,
                                    Category = string.IsNullOrEmpty(subscriptionCreatedResult.Category) ? UnEmpInsuranceCategory.A :
                                           (subscriptionCreatedResult.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B),
                                };

                                await _unEmpInsuranceService.CreateNewSubscriptionFromB2BCompleted(_unEmpInsurancePayment, tryGetUser.Data, subscriptionCreatedResult.PaymentOption, UnEmpInsurancePartnerType.DirectClient, subscriptionCreatedResult.IsSalaryDebit);
                            }
                        }
                    }

                    if (string.Equals(serviceBusMessage.Type, "PushedToSalaryQueueEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var subscriptionInSalaryQueueEvent = JsonConvert.DeserializeObject<UnEmploymentInsuranceAddedInQueueMessageDto>(serviceBusMessage.Data);

                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(subscriptionInSalaryQueueEvent.CitizenId);

                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogError($"{serviceBusMessage.Type}_CITIZENID_{subscriptionInSalaryQueueEvent.CitizenId} Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            var _unEmpInsuranceWithPendingPayment = new UnEmpInsurancePayment
                            {
                                UserId = tryGetUser.Data.Id,
                                Amount = subscriptionInSalaryQueueEvent.Amount,
                                AmountCurrency = ConstantParam.DefaultCurrency,
                                AmountVAT = subscriptionInSalaryQueueEvent.VatAmount,
                                AmountVATCurrency = ConstantParam.DefaultCurrency,
                                Fee = subscriptionInSalaryQueueEvent.FeeAmount,
                                FeeCurrency = ConstantParam.DefaultCurrency,
                                TotalAmount = subscriptionInSalaryQueueEvent.TotalAmount,
                                TotalAmountCurrency = ConstantParam.DefaultCurrency,
                                Status = UnEmpInsuranceStatus.PendingPayment,
                                Remarks = subscriptionInSalaryQueueEvent.Remarks,
                                IsSubscribedExternally = false,
                                Source = UnEmpInsuranceSource.Corporate,
                                IsAutoPayment = subscriptionInSalaryQueueEvent.IsAutoPayment,
                                Category = subscriptionInSalaryQueueEvent.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B,
                            };
                            //Defaulted to Direct Client as B2B portal doesn't process insurance for ExhangeHouse clients
                            await _unEmpInsuranceService.AddNewInsuranceFromB2B(_unEmpInsuranceWithPendingPayment, UnEmpInsurancePartnerType.DirectClient);
                        }
                    }
                    if (string.Equals(serviceBusMessage.Type, "RetryingWithExternalDependencyEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var retryingWithExternalDependencyResult = JsonConvert.DeserializeObject<UnEmploymentInsuranceAddedInQueueMessageDto>(serviceBusMessage.Data);

                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(retryingWithExternalDependencyResult.CitizenId);

                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogError($"{serviceBusMessage.Type}_CITIZENID_{retryingWithExternalDependencyResult.CitizenId} Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            var _unEmpInsuranceWithPendingPayment = new UnEmpInsurancePayment
                            {
                                UserId = tryGetUser.Data.Id,
                                Amount = retryingWithExternalDependencyResult.Amount,
                                AmountCurrency = ConstantParam.DefaultCurrency,
                                AmountVAT = retryingWithExternalDependencyResult.VatAmount,
                                AmountVATCurrency = ConstantParam.DefaultCurrency,
                                Fee = retryingWithExternalDependencyResult.FeeAmount,
                                FeeCurrency = ConstantParam.DefaultCurrency,
                                TotalAmount = retryingWithExternalDependencyResult.TotalAmount,
                                TotalAmountCurrency = ConstantParam.DefaultCurrency,
                                Status = UnEmpInsuranceStatus.Retrying,
                                Remarks = retryingWithExternalDependencyResult.Remarks,
                                IsSubscribedExternally = false,
                                Source = UnEmpInsuranceSource.Corporate,
                                IsAutoPayment = retryingWithExternalDependencyResult.IsAutoPayment,
                                Category = retryingWithExternalDependencyResult.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B,
                            };
                            //Defaulted to Direct Client as B2B portal doesn't process insurance for ExhangeHouse clients
                            await _unEmpInsuranceService.AddNewInsuranceFromB2B(_unEmpInsuranceWithPendingPayment, UnEmpInsurancePartnerType.DirectClient, retryingWithExternalDependencyResult.IsWorkerDetailsNotFoundError);
                        }
                    }
                    if (string.Equals(serviceBusMessage.Type, "RetryingWithInternalDependencyEvent", StringComparison.OrdinalIgnoreCase))
                    {
                        var retryingWithInternalDependencyResult = JsonConvert.DeserializeObject<UnEmploymentInsuranceAddedInQueueMessageDto>(serviceBusMessage.Data);

                        var tryGetUser = await _userService.GetUserByCitizenIdForAllApplications(retryingWithInternalDependencyResult.CitizenId);

                        if (!tryGetUser.IsSuccessful)
                        {
                            log.LogWarning($"{serviceBusMessage.Type}_CITIZENID_{retryingWithInternalDependencyResult.CitizenId} Job: Failed, Reason: {tryGetUser.ErrorMessage}, Body = {messageBody}");
                        }
                        else
                        {

                            var _unEmpInsuranceWithPendingPayment = new UnEmpInsurancePayment
                            {
                                UserId = tryGetUser.Data.Id,
                                Amount = retryingWithInternalDependencyResult.Amount,
                                AmountCurrency = ConstantParam.DefaultCurrency,
                                AmountVAT = retryingWithInternalDependencyResult.VatAmount,
                                AmountVATCurrency = ConstantParam.DefaultCurrency,
                                Fee = retryingWithInternalDependencyResult.FeeAmount,
                                FeeCurrency = ConstantParam.DefaultCurrency,
                                TotalAmount = retryingWithInternalDependencyResult.TotalAmount,
                                TotalAmountCurrency = ConstantParam.DefaultCurrency,
                                Status = UnEmpInsuranceStatus.Retrying,
                                Remarks = retryingWithInternalDependencyResult.Remarks,
                                IsSubscribedExternally = false,
                                Source = UnEmpInsuranceSource.Corporate,
                                IsAutoPayment = retryingWithInternalDependencyResult.IsAutoPayment,
                                Category = retryingWithInternalDependencyResult.Category == "A" ? UnEmpInsuranceCategory.A : UnEmpInsuranceCategory.B,
                            };
                            //Defaulted to Direct Client as B2B portal doesn't process insurance for ExhangeHouse clients
                            await _unEmpInsuranceService.AddNewInsuranceFromB2B(_unEmpInsuranceWithPendingPayment, UnEmpInsurancePartnerType.DirectClient);
                        }
                    }
                }
                else
                {
                    log.LogError($"MsgID_{messageId}_UnEmployment Insurance Service Bus Invalid message");
                }
            }
            catch (Exception ex)
            {
                log.LogWarning($"MsgID_{messageId}_UnEmployment Insurance Service Bus Exception : {ex}");
                return;
            }
        }

        #endregion

        #region subscriptions sync
        [FunctionName("BalanceEnquirySubscriptionOccured")]
        public async Task BalanceEnquirySubscriptionOccured([ServiceBusTrigger("%BalanceEnquirySubscription:ServiceBusTopicName%", "%BalanceEnquirySubscription:ServiceBusSubscriptionName%", Connection = "BalanceEnquirySubscription:ServiceBusConnectionString")] ServiceBusReceivedMessage message, ILogger logger)
        {
            try
            {
                var messageBody = Encoding.UTF8.GetString(message.Body);
                var messageId = message.MessageId;
                var content = JsonConvert.DeserializeObject<MassTransitBaseEvent<BalanceEnquiryUserSubscriptionUpdateEvent>>(messageBody);
                var balanceEnquiryUserSubscription = content.Message;
                var res = await _subscriptionService.SyncSubscription(new Core.Models.DTOs.UpdateSubscriptionDto
                {
                    UserId = balanceEnquiryUserSubscription.UserReferenceId,
                    StartDate = balanceEnquiryUserSubscription.StartDate,
                    EndDate = balanceEnquiryUserSubscription.EndDate,
                    SubscriptionId = balanceEnquiryUserSubscription.SubscriptionId,
                    IsSubscribed = balanceEnquiryUserSubscription.IsSubscribed
                });

                if (!res.IsSuccessful)
                {
                    await Task.Delay(500);
                    res = await _subscriptionService.SyncSubscription(new Core.Models.DTOs.UpdateSubscriptionDto
                    {
                        UserId = balanceEnquiryUserSubscription.UserReferenceId,
                        StartDate = balanceEnquiryUserSubscription.StartDate,
                        EndDate = balanceEnquiryUserSubscription.EndDate,
                        SubscriptionId = balanceEnquiryUserSubscription.SubscriptionId,
                        IsSubscribed = balanceEnquiryUserSubscription.IsSubscribed
                    });
                }

                if (!res.IsSuccessful)
                    logger.LogError($"subscription sync failed for user {balanceEnquiryUserSubscription.UserReferenceId} - {balanceEnquiryUserSubscription.SubscriptionId} - {balanceEnquiryUserSubscription.IsSubscribed} - {balanceEnquiryUserSubscription.StartDate} - {balanceEnquiryUserSubscription.EndDate}");

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occured in BalanceEnquirySubscriptionOccured Job");
            }

        }

        [FunctionName("AddAuditTrailLog")]
        public async Task AddAuditTrailLog([ServiceBusTrigger("%AuditTrail:ServiceBusQueueName%", Connection = "AuditTrail:ServiceBusConnectionString")] ServiceBusReceivedMessage message, ILogger logger, CancellationToken cancellationToken)
        {
            try
            {
                var messageBody = Encoding.UTF8.GetString(message.Body);
                var messageId = message.MessageId;

                var content = JsonConvert.DeserializeObject<MassTransitBaseEvent<AddAuditTrailLogCommand>>(messageBody);
                var auditTrailLog = content.Message;

                var portalUser = await _portalUserService.GetUserByNameAsync(auditTrailLog.Username);
                if (!portalUser.IsSuccessful || portalUser.Data == null) return;

                await _auditTrailService.AddAuditTrail(portalUser.Data.Id, portalUser.Data.Email, auditTrailLog.UserId, auditTrailLog.Action, auditTrailLog.PhoneNumber, null);

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occured in AddAuditTrailLog Job");
            }
        }
        #endregion

        #region C3Pay+

        /// <summary>
        /// Command: <see cref="ReverseAtmWithdrawalCommand" />
        /// Handler: <see cref="ReverseAtmWithdrawalCommandHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [FunctionName("C3PayPlusMembershipAtmWithdrawalFeeReversal")]
        [Singleton]
        public async Task C3PayPlusMembershipAtmWithdrawalFeeReversal([TimerTrigger("%C3PayPlusMembership:AtmWithdrawalFeeReversalSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(C3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: Job started at {DateTime.Now}");

                _ = await _mediator.Send(new ReverseAtmWithdrawalCommand());

                logger.LogDebug($"{nameof(C3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(C3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(C3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: {ex}");
            }
        }

        // TODO: Move timer details to settings.
        // TODO: Run the timer 5 mins before lucky draw date.
        [FunctionName("C3PayPlusLuckyDraw")]
        [Singleton]
        public async Task C3PayPlusLuckyDraw([TimerTrigger("%C3PayPlusMembership:LuckyDrawSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(C3PayPlusLuckyDraw)} Job: Job started at {DateTime.Now}");

                _ = await _mediator.Send(new PickC3PayPlusLuckyDrawWinnersCommand());

                logger.LogDebug($"{nameof(C3PayPlusLuckyDraw)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(C3PayPlusLuckyDraw)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(C3PayPlusLuckyDraw)} Job: {ex}");
            }
        }

        [FunctionName("RenewVpnMemberships")]
        [Singleton]
        public async Task RenewVpnMemberships([TimerTrigger("%VpnMembership:RenewalSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("VPN renewal job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(RenewVpnMemberships)} Job: Job started at {DateTime.Now}");

                _ = await _mediator.Send(new VpnMembershipRenewalsCommand());

                logger.LogDebug($"{nameof(RenewVpnMemberships)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(RenewVpnMemberships)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(RenewVpnMemberships)} Job: {ex}");
            }
        }


        // [FunctionName("ProcessC3PayPlusMembershipMoneyTransferRefunds")]
        // [Singleton]
        // public async Task ProcessC3PayPlusMembershipFreeMoneyTransferRefunds([TimerTrigger("%C3PayPlusMembership:FreeMoneyTransferRefundsSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        // {
        //     var isJobEnabled = IsJobExecutionEnabled();
        //     if (!isJobEnabled)
        //     {
        //         logger.LogWarning("Job skipped due to configuration setting.");
        //         return; // Exit early if the job is disabled
        //     }
        //     try
        //     {
        //         logger.LogDebug($"{nameof(ProcessC3PayPlusMembershipFreeMoneyTransferRefunds)} Job: Job started at {DateTime.Now}");

        //         _ = await _mediator.Send(new ProcessC3PayPlusMembershipFreeMoneyTransferRefundsCommand());

        //         logger.LogDebug($"{nameof(ProcessC3PayPlusMembershipFreeMoneyTransferRefunds)} Job: Job completed.");
        //     }
        //     catch (Exception ex)
        //     {
        //         await Console.Error.WriteLineAsync($"Error occurred in {nameof(ProcessC3PayPlusMembershipFreeMoneyTransferRefunds)} Job: {ex}");
        //         logger.LogError(ex, $"Error occurred in {nameof(ProcessC3PayPlusMembershipFreeMoneyTransferRefunds)} Job: {ex}");
        //     }
        // }


        [FunctionName("ProcessC3PayPlusMembershipAtmWithdrawalFeeReversal")]
        [Singleton]
        public async Task ProcessC3PayPlusMembershipAtmWithdrawalFeeReversal([ServiceBusTrigger("%C3PayPlusMembership:ATMWithdrawalRefundsQueueName%", Connection = "C3PayPlusMembership:ATMWithdrawalRefundsQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {

            string messageBody = string.Empty;

            try
            {
                logger.LogDebug($"{nameof(ProcessC3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: Job started at {DateTime.Now}");

                messageBody = requestMessage.Body.ToString();

                var command = JsonConvert.DeserializeObject<ProcessC3PayPlusMembershipAtmWithdrawalRefundsCommand>(messageBody);

                _ = await _mediator.Send(command);

                logger.LogDebug($"{nameof(ProcessC3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ProcessC3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ProcessC3PayPlusMembershipAtmWithdrawalFeeReversal)} Job: {ex}");
            }
        }

        [FunctionName("ProcessC3PayPlusMembershipPostSubscriptionWork")]
        [Singleton]
        public async Task ProcessC3PayPlusMembershipPostSubscriptionWork([ServiceBusTrigger("%C3PayPlusMembership:PostSubscriptionQueueName%", Connection = "C3PayPlusMembership:PostSubscriptionQueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {

            string messageBody = string.Empty;

            try
            {
                logger.LogDebug($"{nameof(ProcessC3PayPlusMembershipPostSubscriptionWork)} Job: Job started at {DateTime.Now}");

                messageBody = requestMessage.Body.ToString();

                //TODO: Add implementation

                logger.LogDebug($"{nameof(ProcessC3PayPlusMembershipPostSubscriptionWork)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ProcessC3PayPlusMembershipPostSubscriptionWork)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ProcessC3PayPlusMembershipPostSubscriptionWork)} Job: {ex}");
            }
        }

        #endregion

        [FunctionName("ValidateKycBlockExclusions")]
        [Singleton]
        public async Task ValidateKycBlockExclusions([TimerTrigger("%KycBlockExclusions:ScheduleTime%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(ValidateKycBlockExclusions)} Job: Job started at {DateTime.Now}");

                _ = await _kycBlockExclusionService.ValidateKycBlockExclusions();

                logger.LogDebug($"{nameof(ValidateKycBlockExclusions)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(ValidateKycBlockExclusions)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(ValidateKycBlockExclusions)} Job: {ex}");
            }
        }

        [FunctionName("ProcessAutoDeleteProfileAndSendUAfterRToRAK")]
        [Singleton]
        public async Task AutoDeleteProfileAndSendUAfterRToRAK([TimerTrigger("%MoneyTransferService:AutoDeleteProfileAndSendUAfterRSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            if (await _featureManager.IsEnabledAsync("Rmt_EnableProcessAutoDeleteProfileAndSendUAfterRToRAK"))
            {
                try
                {
                    logger.LogDebug($"{nameof(AutoDeleteProfileAndSendUAfterRToRAK)} Job: Job started at {DateTime.Now}");

                    await _rmtKycRefinmentService.HandleRmtKycRefinementRecords(logger);

                    logger.LogDebug($"{nameof(AutoDeleteProfileAndSendUAfterRToRAK)} Job: Job completed.");
                }
                catch (Exception ex)
                {
                    logger.LogError($"Error occurred in {nameof(AutoDeleteProfileAndSendUAfterRToRAK)} Job: {ex}");
                }
            }
        }



        [FunctionName("KycUnblockOtherCardsByPassport")]
        public async Task KycUnblockOtherCardsByPassport([ServiceBusTrigger("%KycUnblockByPassport:QueueName%", Connection = "KycUnblockByPassport:QueueConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger, CancellationToken cancellationToken)
        {
            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();

                logger.LogInformation($"KycUnblockOtherCardsByPassport Job: Message received at {DateTime.UtcNow}, Body = {messageBody}");

                var kycUnblockMessageDto = JsonConvert.DeserializeObject<UnblockKycMessageDto>(messageBody);

                if (kycUnblockMessageDto == null)
                {
                    logger.LogError($"KycUnblockOtherCardsByPassport Job: Unknown Message received, Body = {messageBody}");
                    return;
                }

                await _kycUnblockService.KycUnblockOtherCards(kycUnblockMessageDto.CardholderId, kycUnblockMessageDto.PassportId, kycUnblockMessageDto.EmiratesId);

                logger.LogDebug($"SendTransfer Job: Message handled, Body = {messageBody}");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in SendTransfer Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in SendTransfer Job, Body = {messageBody}");
            }
        }

        /// <summary>
        /// Resend Unassigned Rewards
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("ResendUnassignedRewards")]
        [Singleton]
        public async Task ResendUnassignedRewards([TimerTrigger("%RewardService:ResendScheduleTime%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug("ResendUnassignedRewards: Started");

                var enableRewardSpinTheWheel = await _featureManager.IsEnabledAsync(FeatureFlags.EnableRewardSpinTheWheel);
                if (enableRewardSpinTheWheel)
                {
                    await _moneyTransferService.ResendMoneyTransferUnassignedRewards();
                }

                logger.LogDebug("ResendUnassignedRewards: Completed");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred in ResendUnassignedRewards");
            }
        }

        /// <summary>
        /// Daily Job to renew membership
        /// </summary>
        /// <param name="timerInfo"></param>
        /// <param name="logger"></param>
        [FunctionName("C3PRenewMembership")]
        [Singleton]
        public async Task C3PRenewMembership([TimerTrigger("%C3PayPlusMembership:SubscriptionRenewalSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogInformation("C3PRenewMembership: Started");


                logger.LogInformation("C3PRenewMembership: Completed");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred in C3PRenewMembership Job");
            }
        }

        [FunctionName("SalaryGotPaidEvents")]
        public async Task SalaryGotPaidEvents([ServiceBusTrigger("%MoneyTransferService:SalaryGotPaidTopicName%", "%MoneyTransferService:SalaryGotPaidTopicSubscriptionName%", Connection = "MoneyTransferService:SalaryGotPaidTopicConnectionString")] ServiceBusReceivedMessage requestMessage, ILogger logger)
        {

            var isFeatureEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableSalaryEventProcessFromTopic);
            if (!isFeatureEnabled)
            {
                logger.LogWarning("Job skipped due to feature flag setting off - MT_EnableSalaryEventProcessFromTopic");
                return; // Exit early if the queue based processing enabled
            }

            string messageBody = string.Empty;
            try
            {
                messageBody = requestMessage.Body.ToString();

                var salaryGotPaidEventMessage = JsonConvert.DeserializeObject<IEnumerable<SalaryGotPaidMessageDto>>(messageBody);
                if (salaryGotPaidEventMessage == null)
                {
                    logger.LogWarning($"SalaryGotPaidMessageDto Job: Unknown Message received, Body = {messageBody}");
                    return;
                }


                await this._userService.ProcessSalaryGotPaidEvents(salaryGotPaidEventMessage);

            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in SalaryGotPaidMessageDto Job: {ex.ToString()}");
                logger.LogError(ex, $"Error occurred in SalaryGotPaidMessageDto Job, Body = {messageBody}");
            }
        }


        #region C3Pay+
        [FunctionName("RenewC3PayPlusMembership")]
        [Singleton]
        public async Task RenewC3PayPlusMembership([TimerTrigger("%C3PayPlusMembership:RenewalSchedule%", RunOnStartup = false)] TimerInfo timerInfo, ILogger logger)
        {
            var isJobEnabled = IsJobExecutionEnabled();
            if (!isJobEnabled)
            {
                logger.LogWarning("Job skipped due to configuration setting.");
                return; // Exit early if the job is disabled
            }
            try
            {
                logger.LogDebug($"{nameof(RenewC3PayPlusMembership)} Job: Job started at {DateTime.Now}");

                _ = await _mediator.Send(new RenewC3PayPlusMembershipsCommand());

                logger.LogDebug($"{nameof(RenewC3PayPlusMembership)} Job: Job completed.");
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync($"Error occurred in {nameof(RenewC3PayPlusMembership)} Job: {ex}");
                logger.LogError(ex, $"Error occurred in {nameof(RenewC3PayPlusMembership)} Job: {ex}");
            }
        }
        #endregion

        /// <summary>
        /// Processes surcharge transaction messages and updates HasSurchargeFee flag for CardHolders
        /// </summary>
        /// <param name="message">Service Bus message containing surcharge transaction data</param>
        /// <param name="logger">Logger instance</param>
        /// <returns></returns>
        [FunctionName("ProcessSurchargeTransaction")]
        public async Task ProcessSurchargeTransaction(
            [ServiceBusTrigger("%SurchargeService:TopicName%", "%SurchargeService:SubscriptionName%", 
             Connection = "SurchargeService:TopicConnectionString")] 
            ServiceBusReceivedMessage message, 
            ILogger logger)
        {
            try
            {
                logger.LogInformation($"ProcessSurchargeTransaction: Starting to process message with MessageId: {message.MessageId}");

                // Deserialize the message body
                var messageBody = Encoding.UTF8.GetString(message.Body);
                logger.LogDebug($"ProcessSurchargeTransaction: Message body: {messageBody}");

                var transactionMessage = JsonConvert.DeserializeObject<SurchargeTransactionMessage>(messageBody);

                if (transactionMessage == null)
                {
                    logger.LogError("ProcessSurchargeTransaction: Failed to deserialize message");
                    return;
                }

                // Check if this is a surcharge transaction
                var schemeReference = transactionMessage.Clearing?.Txn_Reference?.Scheme_Txn_Reference;
                if (string.IsNullOrEmpty(schemeReference) || !schemeReference.StartsWith("SURCHARGE", StringComparison.OrdinalIgnoreCase))
                {
                    logger.LogDebug($"ProcessSurchargeTransaction: Not a surcharge transaction. Scheme reference: {schemeReference}");
                    return;
                }

                logger.LogInformation($"ProcessSurchargeTransaction: Processing surcharge transaction with scheme reference: {schemeReference}");

                // Extract account number
                var accountNumber = transactionMessage.Payer?.Account?.Account_No;
                if (string.IsNullOrEmpty(accountNumber))
                {
                    logger.LogError("ProcessSurchargeTransaction: Account number is null or empty");
                    return;
                }

                // Update surcharge fee flag using CardHolderService
                var updateResult = await _cardholderService.UpdateSurchargeFeeFlag(accountNumber);
                
                if (updateResult.IsSuccessful)
                {
                    logger.LogInformation($"ProcessSurchargeTransaction: Successfully processed surcharge fee update for PpsAccountNumber: {accountNumber}");
                }
                else
                {
                    logger.LogWarning($"ProcessSurchargeTransaction: Failed to update surcharge fee flag. {updateResult.ErrorMessage}");
                    return;
                }
            }
            catch (JsonException jsonEx)
            {
                logger.LogError(jsonEx, $"ProcessSurchargeTransaction: JSON deserialization error for message {message.MessageId}");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"ProcessSurchargeTransaction: Unexpected error processing message {message.MessageId}");
                throw; // Re-throw to trigger Service Bus retry logic
            }
        }

        private bool IsJobExecutionEnabled()
        {
            // Fetch the setting from configuration (e.g., from App Settings in Azure)
            string isJobEnabledValue = _configuration[_executionEnabledKey];
            return string.Equals(isJobEnabledValue, "true", StringComparison.OrdinalIgnoreCase);
        }
    }
}
