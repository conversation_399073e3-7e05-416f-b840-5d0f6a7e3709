﻿using C3Pay.Core;
using C3Pay.Core.Services;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.Firebase.FCM.V2.Requests;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Globalization;
using System.Management;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using static MassTransit.ValidationResultExtensions;

namespace C3Pay.Services.Helper
{
    public class PushNotificationSenderService : IPushNotificationSenderService
    {
        private readonly IPushNotificationService _pushNotificationService;
        private readonly IResourceFileService _resourceFileHelper;
        private readonly TextInfo _textFormatter = new CultureInfo("en-US", false).TextInfo;
        private readonly Microsoft.Extensions.Logging.ILogger _logger;

        public PushNotificationSenderService(IPushNotificationService pushNotificationService, IResourceFileService resourceFileHelper,
            ILogger<PushNotificationSenderService> logger)
        {
            this._pushNotificationService = pushNotificationService;
            this._resourceFileHelper = resourceFileHelper;
            this._logger = logger;
        }

        public async Task<ServiceResponse> SendRMTProfileCreatedNotification(string deviceToken)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.RMTProfileCreated.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageBody = message,
                MessageTitle = "RMT Profile Created",
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendVpnRenewalNotification(string deviceToken, string code)
        {
            var vpnRenewalText = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.VPNRenewal.txt");
            var vpnRenewalDeepLink = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.VPNRenewalDeepLink.txt");

            var deepLink = vpnRenewalDeepLink.Data;

            var deepLinkWithCode = string.Format(deepLink, code);

            var message = new VpnRenealMessageData()
            {
                message = new Message()
                {
                    token = deviceToken,
                    notification = new Notification()
                    {
                        title = "VPN subscription is renewed",
                        body = vpnRenewalText.Data
                    },
                    data = new Data()
                    {
                        deeplink = deepLinkWithCode,
                        userId = "123" // not sure about this, need to review
                    },

                    apns = new Apns()
                    {
                        payload = new Payload()
                        {
                            aps = new Aps()
                            {
                                alert = new Alert()
                                {
                                    title = "VPN subscription is renewed",
                                    body = vpnRenewalText.Data,
                                    deeplink = deepLinkWithCode
                                },
                                badge = 1,
                                contentavailable = 1,
                                sound = "default"
                            },
                        },
                    },
                }
            };


            string messageJson = JsonConvert.SerializeObject(message, Formatting.Indented);
            await _pushNotificationService.SendPushNotificationWithData(messageJson);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendRegistrationEmiratesIdApprovalNotification(string deviceToken)//Registration
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.RegistrationEmiratesIdApproval.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageBody = message,
                MessageTitle = ConstantParam.RegistrationEmiratesIdApprovalNotificationHeader,
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendRegistrationPassportApprovalNotification(string deviceToken)//Registration
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.RegistrationPassportApproval.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageBody = message,
                MessageTitle = ConstantParam.RegistrationPassportApprovalNotificationHeader,
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendRegistrationRejectionNotification(string deviceToken)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.RegistrationRejection.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageBody = message,
                MessageTitle = ConstantParam.RegistrationRejectionNotificationHeader,
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendReferralCodeUsedNotification(string deviceToken, string name, int remainingCount, decimal amount, bool plural)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.ReferralCodeUsed.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageTitle = string.Format(ConstantParam.ReferralCodeUsedMessageHeader, name),
                MessageBody = string.Format(message, amount, remainingCount, plural ? "s" : string.Empty),
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendReferralRewardCreditedNotification(string deviceToken, decimal amount)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.ReferralRewardCredited.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageTitle = string.Format(ConstantParam.ReferralAmountRewardedMessageHeader, amount),
                MessageBody = string.Format(message, amount),
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendInstantDirectTransferPushNotification(string deviceToken, string name, decimal amount)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.InstantDirectTransferTemplate.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageTitle = ConstantParam.InstantDirectTransferTemplateHeader,
                MessageBody = string.Format(message, _textFormatter.ToTitleCase((name ?? "").ToLower()).Split(' ')[0], amount),
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendClaimedDirectTransferToSenderPushNotification(string deviceToken, string name, decimal amount)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.ClaimedDirectTransferToSenderTemplate.txt");

            var message = result.Data;



            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageTitle = ConstantParam.ClaimedDirectTransferToSenderTemplateHeader,
                MessageBody = string.Format(message, _textFormatter.ToTitleCase((name ?? "").ToLower()).Split(' ')[0], amount),
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendClaimedDirectTransferToReceiverPushNotification(string deviceToken, string name, decimal amount)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.ClaimedDirectTransferToReceiverTemplate.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageTitle = ConstantParam.ClaimedDirectTransferToReceiverTemplateHeader,
                MessageBody = string.Format(message, amount),
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        #region  Bill Payment Notifications

        public async Task<ServiceResponse> SendBillerAmountDueNotification(string deviceToken, string billerNickName, bool isAmountDue, bool noBill, bool invalidDetails)
        {
            string message = string.Empty, messageTitle = string.Empty;

            if (isAmountDue)
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentAmountDue.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentAmountDueNotification;
            }
            else if (invalidDetails)
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentInvalidDetails.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentInvalidDetailsNotification;

            }
            else if (noBill)
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentNoBill.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentNoBillNotification;
            }

            if (!string.IsNullOrEmpty(message) && !string.IsNullOrEmpty(messageTitle))
            {
                message = message.Replace("##nickname##", billerNickName);
                var pushNotificationData = new SendPushNotificationRequest()
                {
                    MessageBody = string.Format(message),
                    MessageTitle = messageTitle,
                    UserRegistrationToken = deviceToken
                };
                await _pushNotificationService.SendPushNotification(pushNotificationData);
            }
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendPaymentNotification(string deviceToken, string errorMessage, string billerNickName)
        {
            string message = string.Empty, messageTitle = string.Empty;

            if (errorMessage == BillPaymentValidationMessage.NoAmountDue.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentNoDueAtPayment.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentNoBillNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.InsufficientBalance.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentCardInsufficientBalance.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentFailedProcessingPayment;
            }
            else if (errorMessage == BillPaymentValidationMessage.InvalidFieldValue.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentDeleteAndAddAgain.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentInvalidDetailsNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.InvalidAmount.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentDeleteAndAddAgain.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentFailedIncorrectAmountNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.BillerNotAvailable.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentBillerUnavailable.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentFailedBillerNotAvailableNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.AmountNotInAllowedRange.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentDeleteAndAddAgain.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentAmountNotInRangeNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.AccountExpiredOrInvalid.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentBillExpired.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentBillExpiredNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.InvalidUserBiller.ToString() ||
                   errorMessage == BillPaymentValidationMessage.InvalidProduct.ToString() ||
                   errorMessage == BillPaymentValidationMessage.AmountOrProductNotNeeded.ToString() ||
                   errorMessage == BillPaymentValidationMessage.NeedAmountOrProduct.ToString() ||
                   errorMessage == BillPaymentValidationMessage.InvalidUserBiller.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentInvalidDetails.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentInvalidDetailsNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.NoAmountDue.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentNoDueAtPayment.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentNoBillNotification;
            }
            else if (errorMessage == BillPaymentValidationMessage.UnblockYourCard.ToString() ||
                errorMessage == ConstantParam.UnableToGetCardBalance.ToString() ||
                errorMessage == BillPaymentValidationMessage.ActivateYourCard.ToString() ||
                errorMessage == BillPaymentValidationMessage.PaymentFailed.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentPaymentFailed.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentFailedProcessingPayment;
            }
            else if (errorMessage == BillPaymentValidationMessage.SuccessfulPayment.ToString())
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentBillPaid.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentProcessedNotification;
            }
            else
            {
                var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.BillPaymentPaymentFailed.txt");
                message = result.Data;
                messageTitle = ConstantParam.BillPaymentFailedProcessingPayment;
            }
            if (!string.IsNullOrEmpty(message) && !string.IsNullOrEmpty(messageTitle))
            {
                message = message.Replace("##nickname##", billerNickName);

                var pushNotificationData = new SendPushNotificationRequest()
                {
                    MessageBody = string.Format(message),
                    MessageTitle = messageTitle,
                    UserRegistrationToken = deviceToken
                };

                await _pushNotificationService.SendPushNotification(pushNotificationData);

            }



            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendSuccessfulUnEmpInsuranceSubscription(string deviceToken, string phoneNumber, string policyNumber)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.SuccessfulInsuranceSubscription.txt");

            var template = result.Data;

            var message = string.Format(template, policyNumber);

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageBody = string.Format(message),
                MessageTitle = ConstantParam.SuccessfulInsuranceSubscription,
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendMobileRechargeResultNotification(string deviceToken, Status rechargeStatus)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.MobileRechargeDeepLinkStatus.txt");

            var data = new FcmNotificationRequestDto()
            {
                Message = new FcmNotificationMessageDto()
                {
                    Token = deviceToken,
                    Apns = new FcmNotificationApns()
                    {
                        Payload = new FcmNotificationPayload()
                        {
                            Aps = new FcmNotificationAps()
                            {
                                ContentAvailable = 1
                            }
                        }
                    },
                    Data = new MobileRechargeFcmNotificationData()
                    {
                        Title = ConstantParam.MobileRechargePaymentStatus,
                        Body = rechargeStatus.GetDescription().ToString(),
                        Status = rechargeStatus == Status.SUCCESSFUL ? "Success" : "Failure",
                        Deeplink = result.Data
                    }
                }
            };

            //temp logging only
            string jsonString = JsonConvert.SerializeObject(data, Formatting.Indented);
            _logger.LogInformation($"Push Notification data: {jsonString}");

            await _pushNotificationService.SendPushNotificationWithData(data);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendC3PayPlusBalanceEnquiryRefundNotification(string deviceToken)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.C3PayPlusBalanceEnquiryRefund.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageBody = string.Format(message),
                MessageTitle = "Balance Enquiry Refund",
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SendC3PayPlusSmsRefundNotification(string deviceToken)
        {
            var result = await _resourceFileHelper.GetResourceTextContent("C3Pay.Services.Helper.Templates.PushNotification.C3PayPlusSmsRefund.txt");

            var message = result.Data;

            var pushNotificationData = new SendPushNotificationRequest()
            {
                MessageBody = string.Format(message),
                MessageTitle = "SMS Refund",
                UserRegistrationToken = deviceToken
            };

            await _pushNotificationService.SendPushNotification(pushNotificationData);
            return new ServiceResponse();
        }
        #endregion
    }

    public class Alert
    {
        public string title { get; set; }
        public string body { get; set; }
        public string deeplink { get; set; }
    }

    public class Apns
    {
        public Payload payload { get; set; }
    }

    public class Aps
    {
        public Alert alert { get; set; }

        [JsonProperty("content-available")]
        public int contentavailable { get; set; }
        public int badge { get; set; }
        public string sound { get; set; }
    }

    public class Data
    {
        public string userId { get; set; }
        public string deeplink { get; set; }
    }

    public class Message
    {
        public string token { get; set; }
        public Notification notification { get; set; }
        public Data data { get; set; }
        public Apns apns { get; set; }
    }

    public class Notification
    {
        public string title { get; set; }
        public string body { get; set; }
    }

    public class Payload
    {
        public Aps aps { get; set; }
    }

    public class VpnRenealMessageData
    {
        public Message message { get; set; }
    }
}
