﻿{
  "ConnectionStrings": {
    "C3PayConnection": "Data Source=eae-c3pay-sqlsrv-a.database.windows.net;Initial Catalog=C3Pay; User Id=EdenredAdmin; Password=****************",
    "C3PayConnectionReadOnly": "Data Source=WAEAEDXB01-0536;Initial Catalog=C3PayReadOnly;Integrated Security=SSPI; User Id=sa; Password= ************",
    "AzureBlobStorage": "UseDevelopmentStorage=true",
    "RedisConnection": "eae-redis-a.redis.cache.windows.net:6380,password=a8xyPSUYs6Mzluz20Ir1tgdyIRUjlINUQAzCaAPSqAg=,ssl=True,abortConnect=False",
    "AzureAppConfig": "Endpoint=https://eae-c3pay-ac-a.azconfig.io;Id=****;Secret=77RUwCHyLyx+bWPpVx6GZ8ZP9NOvDHMIHDPXOPDaXGs=",
    "ServiceBusConnection": "Endpoint=sb://eae-c3pay-bus-a.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=Thb43rIfWwjf9SM80BflF4x91C9S7MkOHBYgnY+3TaI="
  },
  "General": {
    "MobileAppHashKey": "AUULgijIhOV",
    "EnableSwagger": "",
    "DBPoolSize": 200,
    "CachedEntityExpiryInMinutes": 60,
    "FirstBlackV1PlasticCardId": "3010688213",
    "FirstBlackV2PlasticCardId": "3011215976",
    "SkipOtp": true,
    "EnableRedis": true
  },

  "PasswordValidationRules": {
    "RequiredLength": 6,
    "RequiredUniqueChars": 0,
    "RequireNonAlphanumeric": false,
    "RequireLowercase": false,
    "RequireUppercase": false,
    "RequireDigit": true,
    "RequireLetter": true
  },

  "DirectTransfer": {
    "MaxDirectTransferBeneficiariesCount": 20,
    "MinAmoutToSend": 1,
    "MaxAmountToSend": 1000,
    "MaxAmountToSendPerMonth": 1000,
    "Fee": 0.00,
    "VAT": 0.00
  },

  "IpRateLimiting": {
    "EnableEndpointRateLimiting": true,
    "StackBlockedRequests": false,
    "RealIPHeader": "X-Real-IP",
    "ClientIdHeader": "X-ClientId",
    "HttpStatusCode": 429,
    "GeneralRules": [
      {
        "Endpoint": "*:/api/Test/test-ip-limiting",
        "Period": "1h",
        "Limit": 3
      }
    ]
  },
  "EDConnect": {
    "Authority": "https://sso.sbx.edenred.io",
    "ApiName": "erae-payrol-beneficiary-api",
    "ApiSecret": "qxsBXT-qqI8pSe2fIRdDGeFEsS3b2G4U-OXagfm4",
    "EnableCaching": true,
    "CacheDurationInMinutes": 5
  },

  "EdenredIdentityManager": {
    "Tenant": "AE",
    "BaseAddress": "https://identity.sbx.eu.edenred.io",
    "Authority": "https://login.microsoftonline.com/4c1d9e0f-5c27-4228-a35a-de7b4083ff7b/",
    "ResourceId": "e0e07936-78c6-4692-b736-2b3d3d38ebc9",
    "ClientId": "8b377576-4a61-4a97-a6f4-b1761a60975e",
    "ClientSecret": "**********************************"
  },

  "AADSecurity": {
    "Authority": "https://login.microsoftonline.com/fac4d6fb-1586-4f6a-8e82-89f5c606f39d/",
    "Audience": "5a70dab3-8959-47ee-b981-1894ecb1baf3",
    "AllowedClientIds": "c06bd583-be5e-435b-b29b-bd846fb08013;18e4da3a-80e3-4da2-b22b-f13c917750bd"
  },

  "SendGrid": {
    "SenderEmail": "<EMAIL>",
    "APIKey": "*********************************************************************",
    "Templates": [
      {
        "Name": "BankStatement",
        "TemplateId": "d-d956e6289e0142e3bd84823a356108d8"
      },
      {
        "Name": "RMTProfileCreated",
        "TemplateId": "d-d93f77a2c52a44e387bf12f3b318c174"
      }
    ]
  },

  "EtisalatSMS": {
    "BaseAddress": "https://smartmessaging.etisalat.ae:5676/",
    "SenderName": "C3Pay",
    "ContentType": "application/json",
    "Username": "c3pay",
    "Password": "eGrP$pGeJ41"
  },

  "CleverTapService": {
    "BaseAddress": "https://api.clevertap.com/1/",
    "PassCode": "GOM-QSX-WHUL",
    "ProjectId": "TEST-ZRK-WR7-9Z6Z",
    "EventsNames": {
      "MoneyTransferStarted": "RMT_status_started",
      "MoneyTransferSuccessful": "v2_transfer_make_transfer_success",
      "AutoReversedBankTransfer": "transfer_reversed",
      "MoneyTransferFailed": "v2_transfer_make_transfer_failed",
      "EmiratesIdVerified": "EmiratesIDVerified",
      "EmiratesIDRejected": "EmiratesIDRejected",
      "MoneyTransferProfileCreated": "RmtProfileCreated",
      "C3PayPlusAtmWithdrawalFeeReversal": "ATM_reversal_done",
      "MobileRechargeSuccessRatesExp": "Ratexp_recharge_successful",
      "MobileRechargeFailedRatesExp": "Rateexp_recharge_failed",
      "MRAutoRenewalFailureEvent": "Auto_renewal_failure",
      "MobileRechargeFailureEvent": "Mobile_recharge_failure"
    }
  },

  "InfobipSMS": {
    "BaseAddress": "http://api.infobip.com/api/v3/",
    "SenderName": "C3Pay",
    "ContentType": "application/json",
    "Username": "c3_card",
    "Password": "Test4321"
  },

  "FirebaseCloudMessaging": {
    "BaseAddress": "https://fcm.googleapis.com/fcm/",
    "Key": "AAAAru9kdF8:APA91bGG82P981miySZVK3gY1fGQdHojILaPtknM-OBlJBeeiirJ3UrLw9nkxkV6a_fzI4jNwbyy3fGacfIBV8rBh0kLcvUahXdaonT-QLiFDbP4aPLVq9bOFgNSOubqLnQmp1xVjLFo",
    "SenderId": "751340647519"
  },

  "SignzyService": {
    "BaseAddress": "https://preproduction.signzy.tech/api/",
    "FileExchangeBaseAddress": "https://preproduction-persist.signzy.tech/api/",
    "Id": "iiUy3XpWCp9TlO5yLkEk8R6g3FnHYvG45QLVuu4dfaLx7EfuWJrjR7Hpn6kGu3wa",
    "UserId": "5e9d6f8bdd358091a79d8766",
    "AcceptedSelfieScore": 0.5,
    "AcceptedEIDScore": 0.5,
    "IdealFaceMatchPercentage": 60,
    "IdealNameMatchPercentage": 75,
    "TTL": "2 hrs",
    "MimeType": "image/png",
    "ContentType": "application/json"
  },

  "KYCService": {
    "BaseAddress": "https://az-uatsrvr02.c3card.com:48433/kyc/",
    "MethodName": "ApproveKYC",
    "Username": "c3cardesmows",
    "Password": "Sunday28102017",
    "SponsorCode": "1001MYC3",
    "UniqueRef": "C3WPS201029142246",
    "SharedSecret": "C20abCd17eFgH10iJKl28mNOP3",
    "QueueConnectionString": "Endpoint=sb://eae-c3pay-bus-a.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=UWNLA55uxUXZ/vMAO2j1tuv+7A/sORvkxR+Zz+XaK38=;",
    "QueueName": "kycservice"
  },

  "ESMOService": {
    "Username": "c3cardesmows",
    "Password": "Sunday28102017",
    "SponsorCode": "1001MYC3",
    "SharedSecret": "C20abCd17eFgH10iJKl28mNOP3",
    "QueueConnectionString": "Endpoint=sb://eae-c3pay-bus-a.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=zRWt8YcJY7UIcJqirsDYdW87bqQGspo9kcyt9kheNEI=;",
    "QueueName": "esmoservice",
    "EndpointAddress": "https://uatsrvr02.c3card.com:48433/c3cardexternalapi/ExternalAPI/",
    "BaseAddress": "https://az-uatsrvr01.c3card.com/C3InternalWebAPI/InternalAPICaller/",
    "Authority": "https://login.microsoftonline.com/fac4d6fb-1586-4f6a-8e82-89f5c606f39d/",
    "ClientId": "e9889f1b-68df-4cf7-bc46-b2f128231159",
    "ClientSecret": "****************************************",
    "Scope": "e9889f1b-68df-4cf7-bc46-b2f128231159"
  },

  "KeyVault": {
    "Authority": "https://eae-c3pay-uaen1-kv-a.vault.azure.net/",
    "KeyIdentifier": "https://eae-c3pay-uaen1-kv-a.vault.azure.net/keys/8EF657B0-A277-4C08-8523-1F0402A2FC64/812c68311e8543a2b8cb9457d44cfc8b"
  },

  //"PPSService": {
  //  // Shared
  //  "WebAuthContentType": "application/xml",
  //  "SponsorCode": "*************",
  //  "CustomerCode": "*************",
  //  "Username": "c3chopinws",
  //  "WebAuthClientId": "C3-Apps",
  //  "SpendPolicyReference": "DefaultSpendPolicy",
  //  "SpendPolicyLimitAmount": "600",
  //  "WebAuthBaseURL": "https://webauth.pps.edenred.com/webauth/xmlauth",
  //  "WebAuthClientSecretkey": "bo$jI6YIDj|V2%Y0EVtB",
  //  "EndpointAddress": "https://services.pps.edenred.com/wschopinapi/services/wschopin",
  //  "Password": "Friday14102016",
  //  "SharedSecret": "78Px9No7NfgJBcYwhJOCcJSD"
  //},

  "PPSService": {
    // Shared
    "WebAuthContentType": "application/xml",
    "SponsorCode": "*************",
    "CustomerCode": "*************",
    "Username": "c3chopinws",
    "WebAuthClientId": "C3-Apps",
    "SpendPolicyReference": "SpendPolicy100",
    "SpendPolicyLimitAmount": "600",

    // UAT
    "WebAuthBaseURL": "https://staging01-webauth.pps.edenred.com/webauth/xmlauth",
    "WebAuthClientSecretkey": "z=+KzNX+fYcww08w2/q",
    "EndpointAddress": "https://staging01-services.pps.edenred.com/wschopinapi/services/wschopin",
    "Password": "password",
    "SharedSecret": "5pr0GT35ting",
    "Timeout": 100
  },

  "RAKService": {
    // Shared
    "WebAuthContentType": "application/xml",
    "SponsorCode": "*************",
    "CustomerCode": "*************",
    "Username": "c3chopinws",
    "WebAuthClientId": "C3-Apps",
    "SpendPolicyReference": "SpendPolicy100",
    "SpendPolicyLimitAmount": "600",
    "ProcessBankTransactionsReportsSchedule": "23:00:00",
    "UpdatePendingBankTransactions": "00:10:00",



    // UAT
    "UpdatePendingTransactionsJobStartDate": "2022-10-10 00:00:00",
    "WebAuthBaseURL": "asdasdasdasdasd://staging01-webauth.pps.edenred.com/webauth/xmlauth",
    "WebAuthClientSecretkey": "z=+KzNX+fYcww08w2/q",
    "EndpointAddress": "ffffffffffffffffff://staging01-services.pps.edenred.com/wschopinapi/services/wschopin",
    "Password": "password",
    "SharedSecret": "5pr0GT35ting",
    "BanksMaxRecords": 100,
    "ContentType": "application/json",
    "BeneficiaryQueueConnectionString": "Endpoint=sb://eae-c3pay-bus-a.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=Glve685L+nWxcufLTr7S2xldlLZGQ//1TzIk6Uu4ZrQ=;",
    "BeneficiaryQueueName": "beneficiary",
    "MoneyTransferQueueConnectionString": "Endpoint=sb://eae-c3pay-bus-a.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=96KdR5RStIn6aRB2hhdrM4EzprX2UgVKRDEH2eo5Jb4=;",
    "MoneyTransferQueueName": "moneytransfer",
    "MoneyTransferBeneficiaryCount": 20,
    "BaseURL": "https://testapi.rakbank.ae",
    "URLPath": "/rb/api4",
    "ClientId": "17755b8d-0b3a-4cd8-a05f-7b6c1bbca377",
    "clientSecretkey": "uE3mY8mM2lN7oV4cL5iD4aR6dP1vJ3sH8gY0qC2fV5nG4qH2gO",
    "TokenContentType": "application/x-www-form-urlencoded",
    "TokenScope": "utility_other_banks utility_fxrate beneficiary_management send_money",
    "TokenGrantType": "client_credentials",
    "SSLCertificateName": "RAKServiceSSL",
    "SSLCertificatePassword": "",
    "PaylaodPrivateKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC9Lp9BRPmLGB4V\nEzEWC/Me1H5CcCAGEY6n80LB/dMmasmvV8NhTsLGBDNE+hlF28B2sVd2y1T7L+dn\nZOSpxD98ETrzkAF4tt3yt5UTGeW/4miQy2AunizfHfdIQ/pUtg+y78fNF69E/rDe\ngmWYmwT+Bsww39sPQHnetbstrfuU/qAQ83PErh3zFHSYKhuxnuD1ooIEp/9zeWap\nHVmi/xsTkQdaPOoTLj/L1FxSZPLhsZpQsaL5SN65YwQ4LXST5vlSsTqMlbcpkByw\nSnQt/kENB9FLjQbQ9Y3Gvj3Y8ClfLpYcbzQ3HPLJfXls0CBdlt7W4mT7CtdGOIuR\n3kMK4MYzAgMBAAECggEAGYkHefnjbQDuXo3enEk0ob6w5CraR7dq+AR/yEuIirDz\nVDWWa7YD2FKM0QypCKLm0Z6SXbiIWfdXVr9plfilUQvKV0Hi0PZt8usQtilSt5nJ\n33F0JBa5m8whqqFyUqjYaaCv6WD/CWMPYJldOUIDZH5qZVZx7RcY3BTPlRTc1nlk\nI1QaclA0/1HOP4cbE+Uwygb4Ap6YlOZcLrdxfQv8e7P9IfmSqNa/hEFNJG9pBYD5\nHjODEaRaDli6JOGm1BLhgMuJzGYiy0yp3HF1tY13C65napyHrUlrUSBorViFt5/n\n83T3uuECsA6p/spG0qpFRLd/mDawTcylCK2qNDYtsQKBgQD5sY1YG+tLlPFq/vAg\n8Un8zUC9LrdUrESOrPqrFtmsz/GmNdTYrAFEzRFtI+3a7ICxl13A2/m7I973bS+1\nn5JpePeQsHiqsW7p2xYwahh19jV+KGuZ572hMT+ZaFGZk8aaoWwQR+0jWZWYI6uR\nnvY1ieat8s10rxJun1ge0Y0Q/wKBgQDB9dHxkaX1vuNEXYOGykdxhYRDi5sHZkoN\n1t8/HZys9NXR1Y315gGeW/s13BZG/LcrxaNBwWnjUV6HrpQRF9GX57OvTWKVxnS+\nHb8c10+jHokTYf0vRl9SjSsDMgRmAGBpldd3DarummhtqKK+33irLqHgmQum37O4\nMvs0qkvWzQKBgHSUwMC9sFuGvD1PsMBAyGG/V5W85R4knJHdE+Cj95gnMtV+1tUf\nYixEbvl9SAXqXKAOY1iznGMS+XyevyLW0V5re3NT4dMYqweHIlIShz80aH9x0eN2\n/uPvAZnXyhmhlJ4H9lOXsZHKtvzk0qtYA+61Zz4aWnE7eKirv1IMVwfzAoGANufX\nXoLUcBRxUVEHgoiQYNpi7pv+bsHOBMzmtX80cuF8BcJmRU2u950iJ8T9qWqwj3uL\nE3ok92x5Tf9letE7S70TaHSHi856Dbdt+kQZDxv9wbbjbRaGtV+w5V6rUBSbNkhs\njg8YbCLZnPE34MBx8ENrH6EzYzHANkx4QBhVD6ECgYBJlg9rzBKkdTNT2Pi789RL\n6lJkNtPyHgRjAg9/ztpr9WidufgXkc18K7WZgsnAdYlkepLttrDRK/BCvcE5r9LK\nDMlfQXPvEhc4voS27YRm/5ujWeK8CBa51z24gnXJtA1737xsKGCUkSmXTFOf60Jp\nxG7INSPxUaJ5OHRwu3I6Wg==",
    "PaylaodPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0SeSP9x3lWqBC5kfkhCFoIHq88jFEMRaKhIjVujIzsnecEa62Y0bjUAryrs/xegDNT15CDAES+ZlF1HZDNY/6z/KCVgzcttn18q+tOCmt8bLe4+xHUARQzWkq+u+x+Xyl2CHLR8Hcl9hOrCt9f7kGuf8iTXzwM3WbwZt6huILUB0UW706eCNR1MRy5gIP1M/BPTZxRhDWOCspHltQOH1QrUr16V17Zpacz4O3KVPcbJE8FXuPHGeCh2+DQvK/z/i7Cjz8k4VwIKH6DjJZ1/xMOWYlAevtgr5AYJRSWd4eock4zqJbBvIhYEiN8HDVoCp9Yf2EvBESD2QxrsmtJBELQIDAQAB",
    "MaxTransactionTriesCount": 1,
    "MessageProcessInDelay": 60,
    "MoneyTransferBeneficiaryDelayInMins": 60,
    "LoyaltyImplementDate": "2020-11-12 00:00:00",
    "LoyaltyLimitCount": 5,
    "LoyaltyLimitAmount": 501
  },
  "MobileRechargeService": {
    "QueueConnectionString": "Endpoint=sb://eae-c3pay-bus-d.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=3M/ucZ7lIAktS52T52Fv2urfhQODGIrgFVpMFlFyNfA=;",
    "QueueName": "mobilerecharge",
    "SynchronizeWithDingSchedule": "04:00:00",
    "UpdateStatusSchedule": "03:00:00",
    "MonthlyAmountLimitVerified": 250,
    "MonthlyAmountLimitNotVerified": 30,
    "NickNameLength": 20,
    "TransactionEnvironment": "UAT",
    "CallingCardAccountNumberLive": "**********",
    "MySalaryFeeMode": 2,
    "FeeAmount": 0.5,
    "C3FeeMode": 2
  },
  "DingService": {
    "BaseURL": "https://api.dingconnect.com/api/V1/",
    "ClientApiKey": "9dimAMpUN4s5aeXSj0fx5S",
    "SecondaryClientApiKey": "9dimAMpUN4s5aeXSj0fx5S",
    "ContentType": "application/json"
  },
  "MoneyTransferService": {
    "EnableRakMock": false,
    "UserMonthlyTransactionAmountLimit": 30000,
    "UserMonthlyTransactionCountLimit": 10,
    "DirectTransferMinAmountToSend": 1,
    "DirectTransferMaxAmountToSend": 1000,
    "DirectTransferMaxAmountToSendPerMonth": 1000,
    "DirectTransferFee": 0.95,
    "DirectTransferVAT": 0.05,
    "DirectTransferMaxBeneficiariesCount": 20,
    "ReversePendingDirectMoneyTransfersDurationInMin": 10080,
    "PopularBanksIconsBaseUrl": "https://eaec3sharedsp.blob.core.windows.net/banks/{0}.png",
    "RateExpiryInMinutes": 60,
    "PopularBanks": [
      {
        "Order": 1,
        "CountryCode": "IN",
        "Name": "STATE BANK OF INDIA"
      },
      {
        "Order": 2,
        "CountryCode": "IN",
        "Name": "PUNJAB NATIONAL BANK"
      },
      {
        "Order": 3,
        "CountryCode": "IN",
        "Name": "CANARA BANK"
      },
      {
        "Order": 4,
        "CountryCode": "IN",
        "Name": "UNION BANK OF INDIA"
      },
      {
        "Order": 5,
        "CountryCode": "IN",
        "Name": "FEDERAL BANK"
      },
      {
        "Order": 6,
        "CountryCode": "IN",
        "Name": "BANK OF BARODA"
      },
      {
        "Order": 7,
        "CountryCode": "IN",
        "Name": "CENTRAL BANK OF INDIA"
      },
      {
        "Order": 8,
        "CountryCode": "IN",
        "Name": "INDIAN BANK"
      },
      {
        "Order": 9,
        "CountryCode": "IN",
        "Name": "HDFC BANK"
      },
      {
        "Order": 10,
        "CountryCode": "IN",
        "Name": "BANK OF INDIA"
      },
      {
        "Order": 1,
        "CountryCode": "PK",
        "Name": "UNITED BANK LIMITED"
      },
      {
        "Order": 2,
        "CountryCode": "PK",
        "Name": "HABIB BANK LTD"
      },
      {
        "Order": 3,
        "CountryCode": "PK",
        "Name": "MCB BANK LIMITED"
      },
      {
        "Order": 4,
        "CountryCode": "PK",
        "Name": "ALLIED BANK LIMTED"
      },
      {
        "Order": 5,
        "CountryCode": "PK",
        "Name": "MEEZAN BANK LTD"
      },
      {
        "Order": 6,
        "CountryCode": "PK",
        "Name": "NATIONAL BANK PAKISTAN"
      },
      {
        "Order": 7,
        "CountryCode": "PK",
        "Name": "BANK AL FALAH LIMITED"
      },
      {
        "Order": 8,
        "CountryCode": "PK",
        "Name": "BANK AL HABIB"
      },
      {
        "Order": 9,
        "CountryCode": "PK",
        "Name": "BANK OF PUNJAB"
      },
      {
        "Order": 10,
        "CountryCode": "PK",
        "Name": "ASKARI BANK LIMITED"
      },
      {
        "Order": 1,
        "CountryCode": "NP",
        "Name": "NIC ASIA BANK LTD."
      },
      {
        "Order": 2,
        "CountryCode": "NP",
        "Name": "GLOBAL IME BANK LTD."
      },
      {
        "Order": 3,
        "CountryCode": "NP",
        "Name": "NABIL BANK LTD."
      },
      {
        "Order": 4,
        "CountryCode": "NP",
        "Name": "SIDDHARTHA BANK LTD."
      },
      {
        "Order": 5,
        "CountryCode": "NP",
        "Name": "HIMALAYAN BANK LTD."
      },
      {
        "Order": 6,
        "CountryCode": "NP",
        "Name": "SANIMA BANK LTD."
      },
      {
        "Order": 7,
        "CountryCode": "NP",
        "Name": "EVEREST BANK LTD."
      },
      {
        "Order": 8,
        "CountryCode": "NP",
        "Name": "Prabhu Bank"
      },
      {
        "Order": 9,
        "CountryCode": "NP",
        "Name": "NMB BANK LTD."
      },
      {
        "Order": 10,
        "CountryCode": "NP",
        "Name": "RASTRIYA BANIJYA BANK LTD."
      },
      {
        "Order": 1,
        "CountryCode": "BD",
        "Name": "ISLAMI BANK BANGLDESH LTD."
      },
      {
        "Order": 2,
        "CountryCode": "BD",
        "Name": "SONALI BANK LTD."
      },
      {
        "Order": 3,
        "CountryCode": "BD",
        "Name": "DUTCH-BANGLA BANK LTD"
      },
      {
        "Order": 4,
        "CountryCode": "BD",
        "Name": "AGRANI BANK LTD."
      },
      {
        "Order": 5,
        "CountryCode": "BD",
        "Name": "PUBALI BANK LTD."
      },
      {
        "Order": 6,
        "CountryCode": "BD",
        "Name": "BANK ASIA LTD."
      },
      {
        "Order": 7,
        "CountryCode": "BD",
        "Name": "JANATA BANK LTD."
      },
      {
        "Order": 8,
        "CountryCode": "BD",
        "Name": "AL-ARAFAH ISLAMI BANK LTD."
      },
      {
        "Order": 9,
        "CountryCode": "BD",
        "Name": "NATIONAL BANK LTD."
      },
      {
        "Order": 10,
        "CountryCode": "BD",
        "Name": "SOCIAL ISLAMI BANK LTD"
      },
      {
        "Order": 1,
        "CountryCode": "PH",
        "Name": "BDO BANK"
      },
      {
        "Order": 2,
        "CountryCode": "PH",
        "Name": "BPI FAMILY BANK"
      },
      {
        "Order": 3,
        "CountryCode": "PH",
        "Name": "METROBANK"
      },
      {
        "Order": 4,
        "CountryCode": "PH",
        "Name": "BANK OF THE PHILIPPINE ISLANDS"
      },
      {
        "Order": 5,
        "CountryCode": "PH",
        "Name": "LAND BANK OF THE PHILIPPINES"
      },
      {
        "Order": 6,
        "CountryCode": "PH",
        "Name": "UNION BANK OF THE PHILIPPINES"
      },
      {
        "Order": 7,
        "CountryCode": "PH",
        "Name": "PHILIPPINE NATIONAL BANK"
      },
      {
        "Order": 8,
        "CountryCode": "PH",
        "Name": "SECURITY BANK"
      },
      {
        "Order": 9,
        "CountryCode": "PH",
        "Name": "RCBC SAVINGS"
      },
      {
        "Order": 10,
        "CountryCode": "PH",
        "Name": "EAST WEST BANK"
      },
      {
        "Order": 1,
        "CountryCode": "LK",
        "Name": "COMMERCIAL BANK"
      },
      {
        "Order": 2,
        "CountryCode": "LK",
        "Name": "BANK OF CEYLON"
      },
      {
        "Order": 3,
        "CountryCode": "LK",
        "Name": "SAMPATH BANK"
      },
      {
        "Order": 4,
        "CountryCode": "LK",
        "Name": "PEOPLES BANK"
      },
      {
        "Order": 5,
        "CountryCode": "LK",
        "Name": "Hatton National Bank"
      },
      {
        "Order": 6,
        "CountryCode": "LK",
        "Name": "NATIONAL SAVINGS BANK"
      },
      {
        "Order": 7,
        "CountryCode": "LK",
        "Name": "SEYLAN BANK"
      },
      {
        "Order": 8,
        "CountryCode": "LK",
        "Name": "NATIONS TRUST BANK"
      },
      {
        "Order": 9,
        "CountryCode": "LK",
        "Name": "AMANA BANK"
      }
    ],
    "AutoDeleteProfileAndSendUAfterRSchedule": ""
  },

  "ReferralProgramService": {
    "MoneyTransferCountThreshold": 0,
    "MoneyTransferAmountThreshold": 0,
    "MoneyTransferRewardAmount": 0,
    "MoneyTransferReferralProgramStartDate": "",
    "QueueConnectionString": "",
    "QueueName": ""
  },

  //"RAKService": {
  //  "UpdatePendingTransactionsJobStartDate": "2022-10-10 00:00:00",
  //  // Shared.
  //  "DirectTransferMaxBeneficiariesCount": 20,

  //  // UAT.
  //  "BaseURL": "https://testapi.rakbank.ae",
  //  "ClientId": "17755b8d-0b3a-4cd8-a05f-7b6c1bbca377",
  //  "clientSecretkey": "uE3mY8mM2lN7oV4cL5iD4aR6dP1vJ3sH8gY0qC2fV5nG4qH2gO",
  //  "LoyaltyImplementDate": "2020-11-12 00:00:00",
  //  "LoyaltyLimitCount": 5,
  //  "LoyaltyLimitAmount": 501,
  //  "TokenScope": "utility_other_banks utility_fxrate beneficiary_management send_money",
  //  "TokenGrantType": "client_credentials",
  //  "SSLCertificateName": "RAKServiceSSL",
  //  "SSLCertificatePassword": "peg4sus"
  //  //"BaseURL": "https://api.rakbank.ae",
  //  //"clientSecretkey": "lE6sA1tU8rB6bQ8rQ2pD7mJ4hJ7xI4tS1fQ4kQ3rH4eH8dJ3sY",
  //  //"ClientId": "b27c36b9-0d61-48c3-9901-a64bf3a72dec"
  //  //"PaylaodPrivateKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC9Lp9BRPmLGB4V EzEWC/Me1H5CcCAGEY6n80LB/dMmasmvV8NhTsLGBDNE+hlF28B2sVd2y1T7L+dn ZOSpxD98ETrzkAF4tt3yt5UTGeW/4miQy2AunizfHfdIQ/pUtg+y78fNF69E/rDe gmWYmwT+Bsww39sPQHnetbstrfuU/qAQ83PErh3zFHSYKhuxnuD1ooIEp/9zeWap HVmi/xsTkQdaPOoTLj/L1FxSZPLhsZpQsaL5SN65YwQ4LXST5vlSsTqMlbcpkByw SnQt/kENB9FLjQbQ9Y3Gvj3Y8ClfLpYcbzQ3HPLJfXls0CBdlt7W4mT7CtdGOIuR 3kMK4MYzAgMBAAECggEAGYkHefnjbQDuXo3enEk0ob6w5CraR7dq+AR/yEuIirDz VDWWa7YD2FKM0QypCKLm0Z6SXbiIWfdXVr9plfilUQvKV0Hi0PZt8usQtilSt5nJ 33F0JBa5m8whqqFyUqjYaaCv6WD/CWMPYJldOUIDZH5qZVZx7RcY3BTPlRTc1nlk I1QaclA0/1HOP4cbE+Uwygb4Ap6YlOZcLrdxfQv8e7P9IfmSqNa/hEFNJG9pBYD5 HjODEaRaDli6JOGm1BLhgMuJzGYiy0yp3HF1tY13C65napyHrUlrUSBorViFt5/n 83T3uuECsA6p/spG0qpFRLd/mDawTcylCK2qNDYtsQKBgQD5sY1YG+tLlPFq/vAg 8Un8zUC9LrdUrESOrPqrFtmsz/GmNdTYrAFEzRFtI+3a7ICxl13A2/m7I973bS+1 n5JpePeQsHiqsW7p2xYwahh19jV+KGuZ572hMT+ZaFGZk8aaoWwQR+0jWZWYI6uR nvY1ieat8s10rxJun1ge0Y0Q/wKBgQDB9dHxkaX1vuNEXYOGykdxhYRDi5sHZkoN 1t8/HZys9NXR1Y315gGeW/s13BZG/LcrxaNBwWnjUV6HrpQRF9GX57OvTWKVxnS+ Hb8c10+jHokTYf0vRl9SjSsDMgRmAGBpldd3DarummhtqKK+33irLqHgmQum37O4 Mvs0qkvWzQKBgHSUwMC9sFuGvD1PsMBAyGG/V5W85R4knJHdE+Cj95gnMtV+1tUf YixEbvl9SAXqXKAOY1iznGMS+XyevyLW0V5re3NT4dMYqweHIlIShz80aH9x0eN2 /uPvAZnXyhmhlJ4H9lOXsZHKtvzk0qtYA+61Zz4aWnE7eKirv1IMVwfzAoGANufX XoLUcBRxUVEHgoiQYNpi7pv+bsHOBMzmtX80cuF8BcJmRU2u950iJ8T9qWqwj3uL E3ok92x5Tf9letE7S70TaHSHi856Dbdt+kQZDxv9wbbjbRaGtV+w5V6rUBSbNkhs jg8YbCLZnPE34MBx8ENrH6EzYzHANkx4QBhVD6ECgYBJlg9rzBKkdTNT2Pi789RL 6lJkNtPyHgRjAg9/ztpr9WidufgXkc18K7WZgsnAdYlkepLttrDRK/BCvcE5r9LK DMlfQXPvEhc4voS27YRm/5ujWeK8CBa51z24gnXJtA1737xsKGCUkSmXTFOf60Jp xG7INSPxUaJ5OHRwu3I6Wg==",
  //  //"PaylaodPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt4MubYYoOSnHUsY00kga4ADs6b4x7Q00/onBB0wqcB2i7/mDuViVFjQMdNOI0g21G7pzhb1MKeZ4elZU0Ywq3VnWBNqV6N8NiEtVHnWBROm9+qZpiLeSFjNV4gEzOUnnPCJWxoiAOSHBS+K6ltlaHh747apZj/Heu55CxsIQKe8SgTXjd4nKmzz9JFygRnt+TWdkYYwsdhhZeSIde2+GzVk1iQjS4qdHDEGssstIA+ZnmprkIqPFhcZj78XJoHChLMSzJpKwvxuKStki7TYOjvQavvDNup6WUem8ZVSFg+0oJC44UWT1rA19HfC+SwkSCcgFmQC5HT1CVaIThMoEJQIDAQAB",
  //  //"BeneficiaryQueueConnectionString": "Endpoint=sb://eae-c3pay2-bus-p.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=nJ5st5JMzNnvfaUqCFoUY7Rtck4g8cRXT5Ns45Y9IpY=;",
  //  //"BeneficiaryQueueName": "beneficiary",
  //  //"MoneyTransferQueueConnectionString": "Endpoint=sb://eae-c3pay2-bus-p.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=jLHn+ZTL0qI/Moa/hcmLJHGeURmvGCT3oNF5c5Mo1w4=;",
  //  //"MoneyTransferQueueName": "moneytransfer",
  //  ////"BeneficiaryQueueConnectionString": "Endpoint=sb://eae-c3pay-bus-d.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=GxJym/UGts9DW5n16TwAS3hrCKdAO1zjLaTtbs70StU=;",
  //  ////"BeneficiaryQueueName": "beneficiary",
  //  ////"MoneyTransferQueueConnectionString": "Endpoint=sb://eae-c3pay-bus-d.servicebus.windows.net/;SharedAccessKeyName=ListenSendKey;SharedAccessKey=zxEscFvBQkDzXsoWtdAr9/4WZ0Jq55cPHoDZnRmzBVI=;",
  //  ////"MoneyTransferQueueName": "moneytransfer",
  //  //"MoneyTransferBeneficiaryCount": 20,
  //  //"URLPath": "/rb/api",
  //  //"ContentType": "application/json",
  //  //"TokenContentType": "application/x-www-form-urlencoded",
  //  //"TokenScope": "utility_other_banks utility_fxrate beneficiary_management send_money",
  //  //"TokenGrantType": "client_credentials",
  //  //"SSLCertificateName": "RAKServiceSSL",
  //  //"SSLCertificatePassword": "peg4sus",
  //  ////"PaylaodPrivateKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC9Lp9BRPmLGB4V EzEWC/Me1H5CcCAGEY6n80LB/dMmasmvV8NhTsLGBDNE+hlF28B2sVd2y1T7L+dn ZOSpxD98ETrzkAF4tt3yt5UTGeW/4miQy2AunizfHfdIQ/pUtg+y78fNF69E/rDe gmWYmwT+Bsww39sPQHnetbstrfuU/qAQ83PErh3zFHSYKhuxnuD1ooIEp/9zeWap HVmi/xsTkQdaPOoTLj/L1FxSZPLhsZpQsaL5SN65YwQ4LXST5vlSsTqMlbcpkByw SnQt/kENB9FLjQbQ9Y3Gvj3Y8ClfLpYcbzQ3HPLJfXls0CBdlt7W4mT7CtdGOIuR 3kMK4MYzAgMBAAECggEAGYkHefnjbQDuXo3enEk0ob6w5CraR7dq+AR/yEuIirDz VDWWa7YD2FKM0QypCKLm0Z6SXbiIWfdXVr9plfilUQvKV0Hi0PZt8usQtilSt5nJ 33F0JBa5m8whqqFyUqjYaaCv6WD/CWMPYJldOUIDZH5qZVZx7RcY3BTPlRTc1nlk I1QaclA0/1HOP4cbE+Uwygb4Ap6YlOZcLrdxfQv8e7P9IfmSqNa/hEFNJG9pBYD5 HjODEaRaDli6JOGm1BLhgMuJzGYiy0yp3HF1tY13C65napyHrUlrUSBorViFt5/n 83T3uuECsA6p/spG0qpFRLd/mDawTcylCK2qNDYtsQKBgQD5sY1YG+tLlPFq/vAg 8Un8zUC9LrdUrESOrPqrFtmsz/GmNdTYrAFEzRFtI+3a7ICxl13A2/m7I973bS+1 n5JpePeQsHiqsW7p2xYwahh19jV+KGuZ572hMT+ZaFGZk8aaoWwQR+0jWZWYI6uR nvY1ieat8s10rxJun1ge0Y0Q/wKBgQDB9dHxkaX1vuNEXYOGykdxhYRDi5sHZkoN 1t8/HZys9NXR1Y315gGeW/s13BZG/LcrxaNBwWnjUV6HrpQRF9GX57OvTWKVxnS+ Hb8c10+jHokTYf0vRl9SjSsDMgRmAGBpldd3DarummhtqKK+33irLqHgmQum37O4 Mvs0qkvWzQKBgHSUwMC9sFuGvD1PsMBAyGG/V5W85R4knJHdE+Cj95gnMtV+1tUf YixEbvl9SAXqXKAOY1iznGMS+XyevyLW0V5re3NT4dMYqweHIlIShz80aH9x0eN2 /uPvAZnXyhmhlJ4H9lOXsZHKtvzk0qtYA+61Zz4aWnE7eKirv1IMVwfzAoGANufX XoLUcBRxUVEHgoiQYNpi7pv+bsHOBMzmtX80cuF8BcJmRU2u950iJ8T9qWqwj3uL E3ok92x5Tf9letE7S70TaHSHi856Dbdt+kQZDxv9wbbjbRaGtV+w5V6rUBSbNkhs jg8YbCLZnPE34MBx8ENrH6EzYzHANkx4QBhVD6ECgYBJlg9rzBKkdTNT2Pi789RL 6lJkNtPyHgRjAg9/ztpr9WidufgXkc18K7WZgsnAdYlkepLttrDRK/BCvcE5r9LK DMlfQXPvEhc4voS27YRm/5ujWeK8CBa51z24gnXJtA1737xsKGCUkSmXTFOf60Jp xG7INSPxUaJ5OHRwu3I6Wg==",
  //  ////"PaylaodPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt4MubYYoOSnHUsY00kga4ADs6b4x7Q00/onBB0wqcB2i7/mDuViVFjQMdNOI0g21G7pzhb1MKeZ4elZU0Ywq3VnWBNqV6N8NiEtVHnWBROm9+qZpiLeSFjNV4gEzOUnnPCJWxoiAOSHBS+K6ltlaHh747apZj/Heu55CxsIQKe8SgTXjd4nKmzz9JFygRnt+TWdkYYwsdhhZeSIde2+GzVk1iQjS4qdHDEGssstIA+ZnmprkIqPFhcZj78XJoHChLMSzJpKwvxuKStki7TYOjvQavvDNup6WUem8ZVSFg+0oJC44UWT1rA19HfC+SwkSCcgFmQC5HT1CVaIThMoEJQIDAQAB",
  //  //"BanksMaxRecords": "100",
  //  //"MaxTransactionTriesCount": 2,
  //  //"MessageProcessInDelay": 5,
  //  //"MoneyTransferBeneficiaryDelayInMins": 5,

  //},

  "RAKSFTPConnection": {
    // UAT.
    //"EndPoint": "**************",
    //"Port": 38922,
    //"Username": "C3RAKFTPTEST02",
    //"Password": "tf2bcjhK",
    "InputRootDirectory": "//IN//",
    "OutputRootDirecotry": "//OUT//",
    "TransactionStatusDirectory": "RMT//C3TxnStatus//",
    "BranchesDirectory": "//Branches//New//",
    "BranchesArchiveDirectory": "//Branches//Archive",
    "TransactionBlobContainerName": "raktransaction",
    "ProfileStatusDirectory": ""
  },

  "ExchangeHouseSettings": {
    "BaseAddress": "http://**********:9093",
    "Username": "testuser",
    "Password": "Test@123",
    //"BaseAddress": "http://**********:9093",
    //"Username": "testuser",
    //"Password": "Test@123",
    //"BaseAddress": "https://c3mobapp.indexexchange.ae:444",
    //"Username": "8c2C3N0qu2",
    //"Password": "08cI22N3C2di0NEq2uXE",
    "MaxAllowedBeneficiaryCount": "20",
    "MoneyTransferQueueConnectionString": "",
    "MoneyTransferQueueName": "",
    "DirectTransferMinAmountToSend": 1,
    "DirectTransferMaxAmountToSend": 1000,
    "DirectTransferMaxAmountToSendPerMonth": 1000,
    "DirectTransferFee": 0.95,
    "DirectTransferVAT": 0.05,
    "DirectTransferMaxBeneficiariesCount": 20
  },

  "Testing": {
    "PhoneNumbers": "+971503848301"
  },

  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "Microsoft.EntityFrameworkCore.Database.Command": "Information"
      }
    },
    "Using": [
      "Serilog.Sinks.OpenTelemetry"
    ],
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "OpenTelemetry",
        "Args": {
          "restrictedToMinimumLevel": "Information"
        }
      }
    ]
  },
  "ApplicationInsights": {
    "ConnectionString": ""
  },
  "OpenTelemetry": {
    "ServiceName": "C3Pay.WebJob",
    "ServiceVersion": "1.0.0"
  },
  "Jaeger": {
    "AgentHost": "localhost",
    "AgentPort": 6831,
    "Endpoint": "http://localhost:14268/api/traces"
  },
  "TransactionsB2CService": {
    "Authority": "https://login.microsoftonline.com/4c1d9e0f-5c27-4228-a35a-de7b4083ff7b/",
    "ClientId": "c2fb07dc-8070-4f91-85e2-680ac0f4410a",
    "Scope": "c2fb07dc-8070-4f91-85e2-680ac0f4410a",
    "ClientSecret": "**********************************",
    "BaseAddress": "https://eae-microservice-transactionb2c-web-d.azurewebsites.net",
    "GrantType": "client_credentials",
    "APIVersion": "1"
  },
  "RakBankMoneyTransfer": {
    "BaseURL": "https://testapi.rakbank.ae",
    //"URLPath": "/rb/api4",
    "URLPath": "/rb/api",

    // New
    //"ClientId": "********-3153-44d7-88ab-9b4b9bb2647c",
    //"clientSecretkey": "L3aG4aC7xN6vW5nL2oC8oX7gY6iH6tA5tB3kV0nL4wH3rX4rF7",

    // Old
    "ClientId": "17755b8d-0b3a-4cd8-a05f-7b6c1bbca377",
    "clientSecretkey": "uE3mY8mM2lN7oV4cL5iD4aR6dP1vJ3sH8gY0qC2fV5nG4qH2gO",

    "PayloadPrivateKey": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC9Lp9BRPmLGB4V\nEzEWC/Me1H5CcCAGEY6n80LB/dMmasmvV8NhTsLGBDNE+hlF28B2sVd2y1T7L+dn\nZOSpxD98ETrzkAF4tt3yt5UTGeW/4miQy2AunizfHfdIQ/pUtg+y78fNF69E/rDe\ngmWYmwT+Bsww39sPQHnetbstrfuU/qAQ83PErh3zFHSYKhuxnuD1ooIEp/9zeWap\nHVmi/xsTkQdaPOoTLj/L1FxSZPLhsZpQsaL5SN65YwQ4LXST5vlSsTqMlbcpkByw\nSnQt/kENB9FLjQbQ9Y3Gvj3Y8ClfLpYcbzQ3HPLJfXls0CBdlt7W4mT7CtdGOIuR\n3kMK4MYzAgMBAAECggEAGYkHefnjbQDuXo3enEk0ob6w5CraR7dq+AR/yEuIirDz\nVDWWa7YD2FKM0QypCKLm0Z6SXbiIWfdXVr9plfilUQvKV0Hi0PZt8usQtilSt5nJ\n33F0JBa5m8whqqFyUqjYaaCv6WD/CWMPYJldOUIDZH5qZVZx7RcY3BTPlRTc1nlk\nI1QaclA0/1HOP4cbE+Uwygb4Ap6YlOZcLrdxfQv8e7P9IfmSqNa/hEFNJG9pBYD5\nHjODEaRaDli6JOGm1BLhgMuJzGYiy0yp3HF1tY13C65napyHrUlrUSBorViFt5/n\n83T3uuECsA6p/spG0qpFRLd/mDawTcylCK2qNDYtsQKBgQD5sY1YG+tLlPFq/vAg\n8Un8zUC9LrdUrESOrPqrFtmsz/GmNdTYrAFEzRFtI+3a7ICxl13A2/m7I973bS+1\nn5JpePeQsHiqsW7p2xYwahh19jV+KGuZ572hMT+ZaFGZk8aaoWwQR+0jWZWYI6uR\nnvY1ieat8s10rxJun1ge0Y0Q/wKBgQDB9dHxkaX1vuNEXYOGykdxhYRDi5sHZkoN\n1t8/HZys9NXR1Y315gGeW/s13BZG/LcrxaNBwWnjUV6HrpQRF9GX57OvTWKVxnS+\nHb8c10+jHokTYf0vRl9SjSsDMgRmAGBpldd3DarummhtqKK+33irLqHgmQum37O4\nMvs0qkvWzQKBgHSUwMC9sFuGvD1PsMBAyGG/V5W85R4knJHdE+Cj95gnMtV+1tUf\nYixEbvl9SAXqXKAOY1iznGMS+XyevyLW0V5re3NT4dMYqweHIlIShz80aH9x0eN2\n/uPvAZnXyhmhlJ4H9lOXsZHKtvzk0qtYA+61Zz4aWnE7eKirv1IMVwfzAoGANufX\nXoLUcBRxUVEHgoiQYNpi7pv+bsHOBMzmtX80cuF8BcJmRU2u950iJ8T9qWqwj3uL\nE3ok92x5Tf9letE7S70TaHSHi856Dbdt+kQZDxv9wbbjbRaGtV+w5V6rUBSbNkhs\njg8YbCLZnPE34MBx8ENrH6EzYzHANkx4QBhVD6ECgYBJlg9rzBKkdTNT2Pi789RL\n6lJkNtPyHgRjAg9/ztpr9WidufgXkc18K7WZgsnAdYlkepLttrDRK/BCvcE5r9LK\nDMlfQXPvEhc4voS27YRm/5ujWeK8CBa51z24gnXJtA1737xsKGCUkSmXTFOf60Jp\nxG7INSPxUaJ5OHRwu3I6Wg==",
    "PayloadPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0SeSP9x3lWqBC5kfkhCFoIHq88jFEMRaKhIjVujIzsnecEa62Y0bjUAryrs/xegDNT15CDAES+ZlF1HZDNY/6z/KCVgzcttn18q+tOCmt8bLe4+xHUARQzWkq+u+x+Xyl2CHLR8Hcl9hOrCt9f7kGuf8iTXzwM3WbwZt6huILUB0UW706eCNR1MRy5gIP1M/BPTZxRhDWOCspHltQOH1QrUr16V17Zpacz4O3KVPcbJE8FXuPHGeCh2+DQvK/z/i7Cjz8k4VwIKH6DjJZ1/xMOWYlAevtgr5AYJRSWd4eock4zqJbBvIhYEiN8HDVoCp9Yf2EvBESD2QxrsmtJBELQIDAQAB",
    "TokenGrantType": "client_credentials",
    "TokenScope": "utility_other_banks utility_fxrate beneficiary_management send_money fetch_fields",
    "ContentType": "application/json",
    "SSLCertificateName": "RAKServiceSSL",
    "WesternUnionRatesEnabledCorridors": "USD,XAF,ETB,MAD,KES,AFN,IDR,INR,PKR,BDT,NPR,GHS,EGP,UGX,LKR,PHP,SLE",
    //"WesternUnionRatesEnabledCorridors": "USD,ETB,KES,IDR,GHS,EGP,UGX,XAF,MAD,AFN,SLE",
    "WesternUnionAddBeneficiaryEnabledCorridors": "",
    "WesternUnionSendMoneyEnabledCorridors": ""
  },
  "C3PayPlusMembership": {
    "AtmWithdrawalFeeReversalAmount": "2.5",
    "MembershipDurationInDays": "30",
    "CanWinLuckyDrawAfterInDays": "90",
    "SubscriptionRenewalSchedule": "23:00:00",
    "AllowedPhoneNumbers": ""
  },
  "VpnMembership": {
    "RenewalSchedule": "0 */2 * * *"
  },
  "KycBlockExclusions": {
    "ScheduleTime": "0 0 * * * *",
    "ShouldBeDeletedAfter": 31
  },
  "KycUnblockByPassport": {
    "QueueConnectionString": "",
    "QueueName": ""
  },
  "WebJobExecutionEnabled": false,
  "SurchargeService": {
    "TopicConnectionString": "",
    "TopicName": "surcharge-transactions",
    "SubscriptionName": "surcharge-fee-processor"
  },
  "Queues": {
    "SendSmsNotification": "queue:send-sms-notification"
  }
}
