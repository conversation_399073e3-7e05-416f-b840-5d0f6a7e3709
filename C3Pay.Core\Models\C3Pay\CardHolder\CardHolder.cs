﻿using C3Pay.Core.Models.C3Pay.Lookup;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models
{
    public class CardHolder : BaseModel
    {
        public string Id { get; set; }
        public string EmployeeId { get; set; }
        public string C3RegistrationId { get; set; }
        public string CardSerialNumber { get; set; }
        public string CardNumber { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public DateTime? Birthdate { get; set; }
        public Gender Gender { get; set; }
        public string Nationality { get; set; }
        public string PassportNumber { get; set; }
        public string EmiratesId { get; set; }
        public DateTime? EmiratesIdExpiryDate { get; set; }
        public string EmployeeStatus { get; set; }
        public string CorporateId { get; set; }
        public string CorporateName { get; set; }
        public string City { get; set; }
        public string ZipCode { get; set; }
        public bool BelongsToExchangeHouse { get; set; }
        public string PpsAccountNumber { get; set; }
        public string LabourCardId { get; set; }
        public bool? IsFreeZoneEmployee { get; set; }
        public string AddressLine { get; set; }
        /// <summary>
        /// Indicates if cardholder has been charged a surcharge fee (set via ProcessSurchargeTransaction)
        /// </summary>
        public bool HasSurchargeFee { get; set; }
        public List<User> Users { get; set; }
        public List<ExperimentUsers> ExperimentUsers { get; set; }
        public MissingKycCardholder MissingKycCardholder { get; set; }

        // Place of Birth Information
        public int? BirthProvinceId { get; set; }
        public int? BirthDistrictId { get; set; }
        public Province Province { get; set; }
        public District District { get; set; }


    }
}
