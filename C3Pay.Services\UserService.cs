﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.OutboxMessage;
using C3Pay.Core.Models.C3Pay.User;
using C3Pay.Core.Models.DTOs.UserDtos;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Messages.KYC;
using C3Pay.Core.Models.Messages.Signup;
using C3Pay.Core.Models.Messages.User;
using C3Pay.Core.Models.Portal.SalaryAdvance;
using C3Pay.Core.Models.Portal.Users;
using C3Pay.Core.Models.Settings;
using C3Pay.Core.Services;
using C3Pay.Data;
using C3Pay.Services.Filters;
using C3Pay.Services.Helper;
using C3Pay.Services.IntegrationEvents.Out.Enums;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Card;
using Edenred.Common.Core.Services.Integration;
using Edenred.Common.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

//using System.Runtime.InteropServices.WindowsRuntime;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;
using static Edenred.Common.Core.Enums;
using MobileApplicationId = C3Pay.Core.BaseEnums.MobileApplicationId;

namespace C3Pay.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUnitOfWorkReadOnly _unitOfWorkReadonly;
        private readonly ICardHolderService _cardHolderService;
        private readonly ILogger _logger;
        private readonly IPPSWebAuthService _ppsWebAuthervice;
        private readonly IMessagingQueueService _messagingQueueService;
        private readonly IIdentityService _identityService;
        private readonly ESMOServiceSettings _esmoServiceSettings;
        private readonly ITextMessageSenderService _textMessageSenderService;
        private readonly GeneralSettings _generalSettings;
        private readonly MultimediaSettings _multimediaSettings;
        private readonly MoneyTransferServiceSettings _moneyTransferServiceSettings;

        private readonly RewardServiceSettings _rewardServiceSettings;
        private readonly ReferralProgramServiceSettings _referralProgramServiceSettings;
        private readonly IAuditTrailService _auditTrailService;
        private readonly IAnalyticsService _analyticsService;
        private readonly IAnalyticsPublisherService _analyticsPublisherService;
        private readonly IPPSService _ppsService;
        private readonly IESMOWebService _esmoWebService;
        private readonly C3PayContext _context;
        private readonly ILookupService _lookupService;
        private readonly IPartnerCorporateService _partnerCorporateService;
        private readonly IFeatureManager _featureManager;
        private readonly IKYCService _kycService;
        private readonly IDistributedCache _cacheService;
        private readonly ISanctionScreeningService _sanctionScreeningService;
        private readonly IBenefitService _benefitService;

        private readonly CleverTapServiceSettings _cleverTapServiceSettings;
        private readonly KycExpirySettings _kycExpirySettings;

        private readonly int _passwordLength = 6;
        private readonly char[] _allowedSecondPhoneNumberDigits = new char[] { '0', '2', '4', '5', '6', '8' };
        private readonly int _minSearchLength = 3;
        private readonly string _freeTransferCacheKeyPrefix = "C3_FreeTransferExpiry_";
        private readonly string _mTGoldIncentiveCacheKeyPrefix = "C3_GoldIncentive_";

        public UserService(IUnitOfWork unitOfWork,
                           C3PayContext context,
                           ICardHolderService cardHolderService,
                           ILogger<UserService> logger,
                           IIdentityService identityService,
                           IMessagingQueueService messagingQueueService,
                           IOptions<ESMOServiceSettings> esmoServiceSettings,
                           ITextMessageSenderService textMessageSenderService,
                           IPPSWebAuthService ppsWebAuthervice,
                           IUnitOfWorkReadOnly unitOfWorkReadonly,
                           IOptions<GeneralSettings> generalSettings,
                           IAnalyticsPublisherService analyticsPublisherService,
                           IAnalyticsService analyticsService,
                           IAuditTrailService auditTrailService,
                           IOptions<MoneyTransferServiceSettings> moneyTransferServiceSettings,
                           IOptions<ReferralProgramServiceSettings> referralProgramServiceSettings,
                           IOptions<MultimediaSettings> multimediaSettings,
                           IPPSService ppsService,
                           IESMOWebService esmoWebService,
                           ILookupService lookupService,
                           IPartnerCorporateService partnerCorporateService,
                           IFeatureManager featureManager,
                           IOptions<CleverTapServiceSettings> cleverTapServiceSettings,
                           IOptions<KycExpirySettings> kycExpirySettings,
                           IKYCService kycService,
                           IDistributedCache cacheService,
                           ISanctionScreeningService sanctionScreeningService,
                           IBenefitService benefitService,
                           IOptions<RewardServiceSettings> rewardServiceSettings)
        {
            _referralProgramServiceSettings = referralProgramServiceSettings.Value;
            _moneyTransferServiceSettings = moneyTransferServiceSettings.Value;
            _analyticsPublisherService = analyticsPublisherService;
            _textMessageSenderService = textMessageSenderService;
            _esmoServiceSettings = esmoServiceSettings.Value;
            _multimediaSettings = multimediaSettings.Value;
            _messagingQueueService = messagingQueueService;
            _unitOfWorkReadonly = unitOfWorkReadonly;
            _generalSettings = generalSettings.Value;
            _cardHolderService = cardHolderService;
            _auditTrailService = auditTrailService;
            _ppsWebAuthervice = ppsWebAuthervice;
            _analyticsService = analyticsService;
            _identityService = identityService;
            _esmoWebService = esmoWebService;
            _unitOfWork = unitOfWork;
            _context = context;
            _ppsService = ppsService;
            _logger = logger;
            _lookupService = lookupService;
            _partnerCorporateService = partnerCorporateService;
            _featureManager = featureManager;
            _cleverTapServiceSettings = cleverTapServiceSettings.Value;
            _kycExpirySettings = kycExpirySettings.Value;
            _kycService = kycService;
            _cacheService = cacheService;
            _sanctionScreeningService = sanctionScreeningService;
            _benefitService = benefitService;
            _rewardServiceSettings = rewardServiceSettings.Value;
        }

        public async Task<ServiceResponse<User>> CreateUser(User newUser, BaseEnums.Version version, string cardNumber = null, string cvv = null)
        {
            var userExists = await _unitOfWork.Users.Any(record => (record.CardHolderId == newUser.CardHolderId
            || (record.PhoneNumber == newUser.PhoneNumber && record.ApplicationId == MobileApplicationId.C3Pay)) && !record.IsDeleted);
            if (userExists)
            {
                return new ServiceResponse<User>(false, ConstantParam.UserAlreadyRegistered);
            }

            // Get card holder reference from PPS.
            var tryGetCardHolderReference = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest()
            {
                CardNumber = cardNumber
            });

            if (tryGetCardHolderReference.IsSuccessful == false)
            {
                return new ServiceResponse<User>(false, ConstantParam.InEligibleCard);
            }

            var cardHolderReference = tryGetCardHolderReference.Data;
            var cardHolderResult = await this._cardHolderService.GetCardHolderByCitizenId(cardHolderReference.CardholderRef);

            if (!cardHolderResult.IsSuccessful)
            {
                return new ServiceResponse<User>(false, cardHolderResult.ErrorMessage);
            }

            var cardHolder = cardHolderResult.Data;

            var allowDromantCardsRegistration = await this._featureManager.IsEnabledAsync(FeatureFlags.AllowDromantCardsRegistration);

            if (cardHolder.EmployeeStatus == EmployeeStatus.Dormant.ToString() && !allowDromantCardsRegistration)
            {
                _logger.LogInformation("Blocked dormant card from being registered: {cardSerialNumber}", cardHolder.CardSerialNumber);
                return new ServiceResponse<User>(false, ConstantParam.InEligibleCard);
            }

            var allowedCorporates = await _partnerCorporateService.GetPartnerCorporates();
            //check if card belongs to exchange house that is not listed into partners
            if (cardHolder.BelongsToExchangeHouse)
            {
                var isCorporateAllowed = allowedCorporates.Any(x => x.CorporateId == cardHolder.CorporateId);
                if (!isCorporateAllowed)
                {
                    return new ServiceResponse<User>(false, ConstantParam.InEligibleCard);
                }
            }

            if (version == BaseEnums.Version.V2)
            {
                if (string.IsNullOrEmpty(cvv))
                {
                    return new ServiceResponse<User>(false, ConstantParam.Invalid);
                }

                var cardSerialNumber = cardHolder.CardSerialNumber;

                var kycBlocked = (await _unitOfWork.MissingKycCardholders.FindAsync(u => u.CitizenId == cardHolder.Id
                    && (u.Status == KycStatus.NotSubmittedKycBlocked || u.Status == KycStatus.Blocked))).FirstOrDefault();
                if (kycBlocked != null)
                {
                    var status = await _ppsService.GetCardStatus(cardSerialNumber);
                    if (status.IsSuccessful && status.Data == "Blocked")
                    {
                        // For missing KYC cardholder, we need to first unblock the card so we can verify the CVC.                    
                        var tryUnblockCard = await _ppsService.UnblockCard(cardHolder.CardSerialNumber);
                        if (tryUnblockCard.IsSuccessful == false)
                        {
                            // Can't unblock card.
                            _logger.LogWarning($"Can't unblock card. Error: {tryUnblockCard.ErrorMessage}");

                            return new ServiceResponse<User>(false, CardDetailsResults.IncorrectCvc2.ToString());
                        }
                    }
                    else
                    {
                        kycBlocked = null;
                    }
                }

                var verifyCVVResult = await this._esmoWebService.VerifyCvc2(new VerifyCvc2Request()
                {
                    CardSerialNumber = cardSerialNumber,
                    Cvc2 = cvv
                });

                // Block card again.
                if (kycBlocked != null)
                {
                    var blockedKycCardholder = await _ppsService.BlockCard(cardSerialNumber);
                    if (blockedKycCardholder.IsSuccessful == false)
                    {
                        // Can't unblock card.
                        _logger.LogWarning($"Can't block card. Error: {blockedKycCardholder.ErrorMessage}");

                        return new ServiceResponse<User>(false, CardDetailsResults.IncorrectCvc2.ToString());
                    }
                }

                if (verifyCVVResult.IsSuccessful == false)
                {
                    Enum.TryParse(verifyCVVResult.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                    switch (atmPinError)
                    {
                        case Enums.AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                            await this._cardHolderService.BlockCard(cardSerialNumber);
                            return new ServiceResponse<User>(false, CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                        case Enums.AtmPinErrors.INVALID_CVC2:
                            return new ServiceResponse<User>(false, CardDetailsResults.IncorrectCvc2.ToString());
                        case Enums.AtmPinErrors.GENERAL_ERROR:
                        default:
                            return new ServiceResponse<User>(false, CardDetailsResults.Error.ToString());
                    }
                }
            }

            newUser.CardHolderId = cardHolder.Id;

            var cardHolderExists = await this._unitOfWork.CardHolders.Any(record => record.Id == cardHolder.Id);

            if (!cardHolderExists)
            {
                newUser.CardHolder = cardHolder;
            }


            using (var transaction = _context.Database.BeginTransaction())
            {
                await _unitOfWork.Users.AddAsync(newUser);
                await this._unitOfWork.CommitAsync();
                await AddOutboxMessage(newUser, OutboxMessageTypeEnum.UserCreatedEvent.ToString());
                await this._unitOfWork.CommitAsync();
                await transaction.CommitAsync();
            }


            // Refresh linked user.
            newUser.PhoneNumber = newUser.PhoneNumber.Trim();
            var beneficiaries = await this._unitOfWork.MoneyTransferBeneficiaries.FindAsync(
                b => b.IsDeleted == false
                && b.AccountNumber == newUser.PhoneNumber,
                false, x => x.User, x => x.User.CardHolder);


            if (beneficiaries.Count > 0)
            {
                var userPartnerCode = _partnerCorporateService.GetUserPartnerCode(cardHolder, allowedCorporates);

                foreach (var beneficiary in beneficiaries)
                {
                    var userBeneficiaryPartnerCode = _partnerCorporateService.GetUserPartnerCode(beneficiary.User.CardHolder, allowedCorporates);

                    if (userPartnerCode.Trim().ToLower() == userBeneficiaryPartnerCode.Trim().ToLower())
                    {
                        beneficiary.LinkedUserId = newUser.Id;
                        beneficiary.Status = Status.APPROVED;
                        beneficiary.AccountNumber = null;
                    }
                    else
                    {
                        beneficiary.IsCrossTransfer = true;
                    }

                }

                await this._unitOfWork.CommitAsync();
            }



            var completeUserRegistrationMessage = new CompleteUserRegistrationDto()
            {
                AppStatus = true,
                CardSerialNo = cardHolder.CardSerialNumber,
                CorporateId = long.Parse(cardHolder.CorporateId),
                CitizenId = cardHolder.Id,
                Mobile = newUser.PhoneNumber,
                PpsAccountNumber = cardHolder.PpsAccountNumber
            };

            await _messagingQueueService.SendAsync(completeUserRegistrationMessage, this._esmoServiceSettings.QueueConnectionString, this._esmoServiceSettings.QueueName, null);

            // Only claim Direct Transfer if new user is a C3Pay user.
            if (newUser.ApplicationId == MobileApplicationId.C3Pay)
            {
                var claimPendingDirectTransfersMessages = new ClaimPendingDirectTransfersDto()
                {
                    AccountNumber = newUser.PhoneNumber
                };

                await _messagingQueueService.SendAsync(claimPendingDirectTransfersMessages,
                    this._moneyTransferServiceSettings.ClaimPendingDirectTransfersQueueConnectionString,
                    this._moneyTransferServiceSettings.ClaimPendingDirectTransfersQueueName, null);
            }

            return new ServiceResponse<User>(newUser);
        }

        public async Task AddOutboxMessage(User user, string type)
        {
            var outboxMessage = new OutboxMessage
            {
                Type = type,
                Data = JsonSerializer.Serialize(new
                {
                    PhoneNumber = user.PhoneNumber,
                    UserId = user.Id,
                    ApplicationId = MobileApplicationId.C3Pay
                }),
                CreatedDate = DateTime.UtcNow
            };

            await _unitOfWork.OutboxMessages.AddAsync(outboxMessage);
        }

        public async Task<ServiceResponse<User>> CreateExternalUser(User newUser)
        {
            var userExists = await this._unitOfWork.Users.Any(record => (record.CardHolderId == newUser.CardHolderId || record.PhoneNumber == newUser.PhoneNumber) && !record.IsDeleted);

            if (userExists)
            {
                return new ServiceResponse<User>(false, ConstantParam.UserAlreadyRegistered);
            }

            var cardHolderResult = await this._cardHolderService.GetCardHolderByCitizenId(newUser.CardHolderId);

            if (!cardHolderResult.IsSuccessful)
            {
                return new ServiceResponse<User>(false, cardHolderResult.ErrorMessage);
            }

            var cardHolder = cardHolderResult.Data;
            if (cardHolder.BelongsToExchangeHouse)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserInEligibleForRegistrationByCitizenId, newUser.CardHolderId));
            }

            var cardHolderExists = await _unitOfWork.CardHolders.Any(record => record.Id == cardHolder.Id);
            if (!cardHolderExists)
            {
                newUser.CardHolder = cardHolder;
            }

            await _unitOfWork.Users.AddAsync(newUser);

            await _unitOfWork.CommitAsync();

            if (cardHolderExists)
            {
                newUser.CardHolder = cardHolder;
            }

            return new ServiceResponse<User>(newUser);
        }

        public async Task<ServiceResponse<User>> UpdateExternalUser(User user, string phoneNumber, bool isVerified)
        {
            user.PhoneNumber = phoneNumber;

            user.IsVerified = isVerified;

            await this._unitOfWork.CommitAsync();

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<Tuple<List<User>, int>>> SearchUsers(SearchUserParameters searchUserParameters, SearchType searchType)
        {
            if (ValidateSearch(searchUserParameters))
            {
                var filters = UserFilter.GetFilter(searchUserParameters, searchType);
                var responseUser = await this._unitOfWorkReadonly.Users.Search(filters, searchUserParameters.Page, searchUserParameters.Size);
                return new ServiceResponse<Tuple<List<User>, int>>(responseUser);
            }
            return new ServiceResponse<Tuple<List<User>, int>>(new Tuple<List<User>, int>(new List<User>(), 0));
        }

        public async Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsers(SearchUserParameters searchUserParameters)
        {
            if (ValidateSearch(searchUserParameters) || !string.IsNullOrEmpty(searchUserParameters.RegistrationType))
            {
                var filters = UserFilter.GetFilter(searchUserParameters, SearchType.Verification);
                var responseUser = await this._unitOfWorkReadonly.Users.SearchVerificationUser(filters, searchUserParameters.Page, searchUserParameters.Size, searchUserParameters.RegistrationType);
                return new ServiceResponse<Tuple<List<VerificationUser>, int>>(responseUser);
            }
            return new ServiceResponse<Tuple<List<VerificationUser>, int>>(new Tuple<List<VerificationUser>, int>(new List<VerificationUser>(), 0));
        }
        public async Task<ServiceResponse> BlockUser(User userToBeBlocked, Guid? portalUserId = null, string portalEmailId = null, int? blockReasonId = null)
        {
            this._logger.LogDebug(ConstantParam.BlockingUser, userToBeBlocked.Id, BaseEnums.UserBlockType.BlockedByAdmin);

            var userIdentityResult = await this._identityService.LockUserAccountAsync(userToBeBlocked.PhoneNumber);

            if (!userIdentityResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.BlockingUserFailed, userToBeBlocked.Id, BaseEnums.UserBlockType.BlockedByAdmin, userIdentityResult.ErrorMessage);
                return new ServiceResponse(false, userIdentityResult.ErrorMessage);
            }

            userToBeBlocked.Block(BaseEnums.UserBlockType.BlockedByAdmin);
            userToBeBlocked.UserBlockReasonId = blockReasonId;
            await this._unitOfWork.CommitAsync();

            if (portalUserId != null)
            {
                var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, userToBeBlocked.Id, ConstantParam.AuditTrailBlock.ToString(), userToBeBlocked.PhoneNumber, "");
            }
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UnblockUser(User userToBeUnblocked, string comment, Guid? portalUserId = null, string portalEmailId = null)
        {
            this._logger.LogDebug(ConstantParam.UnblockingUser, userToBeUnblocked.Id, BaseEnums.UserBlockType.BlockedByAdmin);

            var userIdentityResult = await this._identityService.UnLockUserAccountAsync(userToBeUnblocked.PhoneNumber);

            if (!userIdentityResult.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.UnblockingUserFailed, userToBeUnblocked.Id, userIdentityResult.ErrorMessage);
                return new ServiceResponse(false, userIdentityResult.ErrorMessage);
            }

            userToBeUnblocked.Unblock();

            await this._unitOfWork.CommitAsync();

            if (portalUserId != null)
            {
                var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, userToBeUnblocked.Id, ConstantParam.AuditTrailUnblock, userToBeUnblocked.PhoneNumber, null);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> RemoveUserAccount(Guid userIdToRemove, Guid? portalUserId = null, bool checkIfRmtIsPending = false, bool deactivateRmt = false, bool setCardHolderAsDeleted = false, string portalEmailId = null)
        {
            this._logger.LogDebug(ConstantParam.DeletingAccount, userIdToRemove);

            var userResult = await GetUserById(userIdToRemove);
            if (!userResult.IsSuccessful)
            {
                return new ServiceResponse(false, userResult.ErrorMessage);
            }

            var user = userResult.Data;
            if (checkIfRmtIsPending && user.MoneyTransferProfileStatus == MoneyTransferProfileStatus.Pending)
            {
                var userWithIdentifications = await GetUserByIdWithIdentifications(user.Id);
                if (userWithIdentifications.IsSuccessful)
                {
                    var data = userWithIdentifications.Data.Identifications.OrderByDescending(o => o.CreatedDate)
                                                                           .FirstOrDefault(a => a.VerificationStatus == IdentificationVerificationStatus.Approved);
                    if (data != null && (DateTime.Now.Date - data.CreatedDate.Date).TotalDays < 4)
                    {
                        return new ServiceResponse(false, string.Format(ConstantParam.PendingMoneyTransferProfile, data.CreatedDate.AddDays(4).ToString("dd/MM/yyyy")));
                    }
                }
            }

            user.MarkAsDeleted();

            if (setCardHolderAsDeleted)
            {
                user.CardHolder.EmployeeStatus = EmployeeStatus.Deleted.ToString();
            }

            if (deactivateRmt)
            {
                user.MoneyTransferProfileStatus = MoneyTransferProfileStatus.Missing;
                await DeactivateRmt(user.CardHolderId, user.CardHolder.EmiratesId, user.Id, user.CardHolder.BelongsToExchangeHouse);
            }

            // Refresh linked user.
            var beneficiaries = await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(
                b => b.IsDeleted == false
                && b.LinkedUserId == userIdToRemove,
                false);

            foreach (var beneficiary in beneficiaries)
            {
                beneficiary.LinkedUserId = null;
                beneficiary.Status = Status.PENDING;
                beneficiary.AccountNumber = user.PhoneNumber;
            }
            await AddOutboxMessage(user, OutboxMessageTypeEnum.UserRemovedEvent.ToString());
            await this._unitOfWork.CommitAsync();

            var username = user.PhoneNumber.ToZeroPrefixedPhoneNumber();

            var deleteResponse = await this._identityService.DeleteUserAccountAsync(username);
            if (!deleteResponse.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.AccountDeleted, username, deleteResponse.ErrorMessage);
                return new ServiceResponse(false, deleteResponse.ErrorMessage);
            }

            if (portalUserId != null)
            {
                var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, user.Id, ConstantParam.AuditTrailRemoveAccount, username, null);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ResetPassword(User userAccount)
        {
            this._logger.LogDebug(ConstantParam.ResettingPassword, userAccount.Id);

            var resultVerificationCode = await this._identityService.RequestResetPasswordAsync(userAccount.PhoneNumber);

            if (!resultVerificationCode.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.ResettingPasswordFailed, userAccount.Id, resultVerificationCode.ErrorMessage);
                return new ServiceResponse(false, resultVerificationCode.ErrorMessage);
            }

            var temporaryPassword = GenerateDigitPasswordWithSingleAlphabet(_passwordLength);
            var resultResetPassword = await this._identityService.ResetPasswordAsync(userAccount.PhoneNumber, temporaryPassword, resultVerificationCode.Data);

            if (!resultResetPassword.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.ResettingPasswordFailed, userAccount.Id, resultResetPassword.ErrorMessage);

                return new ServiceResponse(false, resultResetPassword.ErrorMessage);
            }


            var resultSendResetPassword = await this._textMessageSenderService.SendResetPasswordMessage(userAccount.PhoneNumber.ToShortPhoneNumber(), temporaryPassword);

            if (!resultSendResetPassword.IsSuccessful)
            {
                this._logger.LogWarning(ConstantParam.SendingPasswordFailed, userAccount.Id, resultSendResetPassword.ErrorMessage);
                return new ServiceResponse(false, resultSendResetPassword.ErrorMessage);
            }
            userAccount.RequiresPasswordReset = true;
            userAccount.MarkAsUpdated();
            await this._unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<bool>> GetUserKYCStatus(Guid id)
        {
            var kycUploaded = await this._unitOfWork.Identifications.Any(record => record.UserId == id &&
            (record.VerificationStatus == IdentificationVerificationStatus.Pending || record.VerificationStatus == IdentificationVerificationStatus.Approved));

            return new ServiceResponse<bool>(kycUploaded);
        }

        public async Task<ServiceResponse<User>> GetUserById(Guid id, CancellationToken cancellationToken = default(CancellationToken))
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == id && !record.IsDeleted, record => record.CardHolder);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFound, id));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<User>> GetUserByIdWithIdentifications(Guid id)
        {
            var user = await this._unitOfWork.Users.Search(id);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFound, id));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<UserProfile>> GetProfile(Guid? id = null, string phoneNumber = null, CancellationToken cancellationToken = default(CancellationToken))
        {
            var user = await _unitOfWork.Users.GetProfile(_referralProgramServiceSettings, id, phoneNumber, _generalSettings, cancellationToken);
            if (user is null)
            {
                return new ServiceResponse<UserProfile>(false, string.Format(ConstantParam.UserNotFoundByPhoneNumber, phoneNumber));
            }

            var allowedCorporates = await this._partnerCorporateService.GetPartnerCorporates();

            var partnerCode = PartnerType.DirectClient.ToString();
            user.PartnerLogoUrl = string.Empty;
            var partnerCorporate = allowedCorporates.FirstOrDefault(x => x.CorporateId == user.CorporateId);
            if (partnerCorporate != null)
            {
                partnerCode = partnerCorporate.Partner.Code;
                user.PartnerLogoUrl = partnerCorporate.Partner.LogoUrl;
            }

            user.Role = user.BelongsToExchangeHouse && partnerCode != PartnerType.DirectClient.ToString() ? UserRole.ExchangeHouseUser.ToString() : UserRole.DirectUser.ToString();

            if (user.Role == UserRole.ExchangeHouseUser.ToString())
            {
                user.RMTProfileStatus = MoneyTransferProfileStatus.Created.ToString();
            }

            user = await LoadUserVideos(user);
            user.EmiratesIdStatus = (await GetUserEmiratesIdStatus(user.IsVerified, user.EmiratesId, user.EmiratesIdExpiryDate, user.HasApprovedEmiratesIdUpdates, user.HasPendingEmiratesIdUpdates, user.HasApprovedPassportUpdates))?.Data.ToString();
            user.PassportStatus = GetUserPassportStatus(user.HasApprovedPassportUpdates, user.HasPendingPassportUpdates).Data.ToString();

            #region Experiments

            //Get active experiments
            var experiments = (await _lookupService.GetActiveExperiments()).Data.ToList();

            //Get Experiments assigned to the user
            var experimentsAssigned = await _unitOfWork.ExperimentUsers.FindAsync(f => f.CardHolderId == user.CitizenId &&
                                                                                       !f.IsDeleted && !f.Experiment.IsDeleted,
                                                                                  i => i.Experiment,
                                                                                  i => i.Experiment.Feature,
                                                                                  i => i.Experiment.ExperimentCtas,
                                                                                  i => i.Experiment.ExperimentGroupMultimedias);
            if (experimentsAssigned != null && experimentsAssigned.Count > 0)
            {
                user.ExperimentUsers = experimentsAssigned.ToList();

                //Filter the ExperimentCTAs based on group code
                if (user.ExperimentUsers.Any(a => a.Experiment.ExperimentCtas.Any()))
                {
                    foreach (var item in user.ExperimentUsers.Where(x => x.Experiment.ExperimentCtas.Any()))
                    {
                        item.Experiment.ExperimentCtas = item.Experiment.ExperimentCtas.Where(x => x.GroupCode == item.GroupCode || x.GroupCode == null).ToList();
                    }
                }

                //Filter the ExperimentMultimedias based on group code
                if (user.ExperimentUsers.Any(a => a.Experiment.ExperimentGroupMultimedias.Any()))
                {
                    foreach (var item in user.ExperimentUsers.Where(x => x.Experiment.ExperimentGroupMultimedias.Any()))
                    {
                        var experimentData = item.Experiment.ExperimentGroupMultimedias.Where(x => x.GroupCode == item.GroupCode && x.NationalityCode == user.Nationality && !x.IsDeleted).ToList();
                        if (experimentData.Count == 0)
                        {
                            experimentData = item.Experiment.ExperimentGroupMultimedias.Where(x => x.GroupCode == item.GroupCode && x.NationalityCode == null && !x.IsDeleted).ToList();
                        }

                        item.Experiment.ExperimentGroupMultimedias = experimentData;
                    }
                }

                //Set the LoginVideoUrl
                var tryCheckingIfLoginVideoExperimentAssigned = experimentsAssigned.FirstOrDefault(x => x.ExperimentId == (int)ExperimentType.LoginVideo);
                if (tryCheckingIfLoginVideoExperimentAssigned != null)
                {
                    var groupCode = tryCheckingIfLoginVideoExperimentAssigned.GroupCode;
                    user.LoginVideoUrl = tryCheckingIfLoginVideoExperimentAssigned.Experiment.ExperimentGroupMultimedias.FirstOrDefault(f => f.GroupCode == groupCode)?.Url;
                }

                var enableFreeTransferExpiry = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableFreeTransferExpiry);
                if (enableFreeTransferExpiry)
                {
                    //Set the FreeTransferExpiryDate
                    var tryCheckingIfFreeTransferExperimentAssigned = experimentsAssigned.FirstOrDefault(x => x.ExperimentId == (int)ExperimentType.FreeTransferExpiry && x.GroupCode == ExperimentGroupCodeType.A.ToString());
                    if (tryCheckingIfFreeTransferExperimentAssigned != null)
                    {
                        var savedInCache = await this._cacheService.GetRecordAsync<bool>(string.Concat(_freeTransferCacheKeyPrefix, user.Id));
                        if (!savedInCache)
                        {
                            await this._cacheService.SetRecordAsync(string.Concat(_freeTransferCacheKeyPrefix, user.Id), true, TimeSpan.FromDays(30));
                        }

                        if (user.FreeTransferExpiryDate < DateTime.Now)
                        {
                            user.LoginVideoUrl = null;
                        }
                    }
                }
                var enableMTGoldIncentiveExperiment = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableGoldIncentiveAcquisition);
                if (enableMTGoldIncentiveExperiment)
                {
                    //Set the MtGoldIncentiveExpiryDate
                    var tryCheckingIfGoldIncentiveExperimentAssigned = experimentsAssigned.FirstOrDefault(x => x.ExperimentId == (int)ExperimentType.GoldIncentiveAcquisition
                    && x.GroupCode == ExperimentGroupCodeType.B.ToString());
                    if (tryCheckingIfGoldIncentiveExperimentAssigned != null)
                    {
                        var savedInCache = await this._cacheService.GetRecordAsync<bool>(string.Concat(_mTGoldIncentiveCacheKeyPrefix, user.Id));
                        if (!savedInCache)
                            await this._cacheService.SetRecordAsync(string.Concat(_mTGoldIncentiveCacheKeyPrefix, user.Id), true, TimeSpan.FromDays(30));

                        if (user.MtGoldIncentiveExpiryDate == null || user.MtGoldIncentiveExpiryDate < DateTime.Now)
                            user.LoginVideoUrl = null;
                    }
                }

                // Execute rates experiment if applicable.
                var isRatesExperimentEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableRatesExperiment);
                if (isRatesExperimentEnabled)
                {
                    var tryCheckingIfRatesExperimentAssigned = experimentsAssigned
                        .FirstOrDefault(x => x.ExperimentId == (int)ExperimentType.RatesExperimentMid
                       || x.ExperimentId == (int)ExperimentType.RatesExperimentBest
                       || x.ExperimentId == (int)ExperimentType.RatesExperimentControl);

                    if (tryCheckingIfRatesExperimentAssigned != null)
                    {
                        int experimentId = tryCheckingIfRatesExperimentAssigned.ExperimentId;

                        var multimedia = tryCheckingIfRatesExperimentAssigned.Experiment
                            .ExperimentGroupMultimedias
                            .FirstOrDefault(f => f.ExperimentId == experimentId);

                        if (multimedia != null)
                        {
                            user.LoginVideoUrl = multimedia.Url;
                        }
                    }
                }
            }

            var userAddedToAnyExperiment = false;
            var enableOddEvenExperiments = await this._featureManager.IsEnabledAsync(FeatureFlags.EnableOddEvenExperiments);
            if (enableOddEvenExperiments)
            {
                //OddEven experiments
                foreach (var experiment in experiments.Where(x => x.ExperimentMode == ExperimentMode.OddEven))
                {
                    if (!experimentsAssigned.Any(x => x.ExperimentId == experiment.Id))
                    {
                        if (double.TryParse(user.CitizenId, out double citizenIdAsNumber))
                        {
                            userAddedToAnyExperiment = true;

                            var experimentUser = new ExperimentUsers();
                            experimentUser.CardHolderId = user.CitizenId;
                            experimentUser.ExperimentId = experiment.Id;
                            experimentUser.NationalityCode = user.Nationality;
                            experimentUser.CreatedDate = DateTime.Now;

                            if (citizenIdAsNumber % 2 == 0)
                                experimentUser.GroupCode = ExperimentGroupCodeType.B.ToString();
                            else
                                experimentUser.GroupCode = ExperimentGroupCodeType.A.ToString();

                            //add the data to the experimentAssigned
                            experimentUser.Experiment = experiment;
                            experimentsAssigned.Add(experimentUser);
                        }
                    }
                }
            }

            var enableStoriesScaling = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableStoriesScaling);
            if (enableStoriesScaling)
            {
                if (user.RMTProfileStatus == MoneyTransferProfileStatus.Created.ToString() && user.Nationality == "IND" && user.MoneyTransferTransactionsCount == 0)
                {
                    var getStoriesExperiment = experiments.FirstOrDefault(x => x.Name == "MoneyTransferStories_Revamp");
                    if (getStoriesExperiment != null && !experimentsAssigned.Any(x => x.ExperimentId == getStoriesExperiment.Id))
                    {
                        userAddedToAnyExperiment = true;

                        var experimentUser = new ExperimentUsers();
                        experimentUser.CardHolderId = user.CitizenId;
                        experimentUser.ExperimentId = getStoriesExperiment.Id;
                        experimentUser.NationalityCode = user.Nationality;
                        experimentUser.CreatedDate = DateTime.Now;
                        experimentUser.GroupCode = ExperimentGroupCodeType.A.ToString();

                        //add the data to the experimentAssigned
                        experimentUser.Experiment = getStoriesExperiment;
                        experimentsAssigned.Add(experimentUser);
                    }
                }
            }

            //SetControlUsersOnly, here we store the 10% of control users in the experiment users list and the rest of the users will be in Variant group
            var enableManualNinetyTenSplit = true; //await _featureManager.IsEnabledAsync(FeatureFlags.EnableSetControlUsersOnlyExperiments);
            if (enableManualNinetyTenSplit)
            {
                var loginVideoExperimentDetails = experiments.FirstOrDefault(f => f.Name == ExperimentType.LoginVideo.ToString());
                foreach (var experiment in experiments.Where(x => x.ExperimentMode == ExperimentMode.Custom))
                {
                    if (!experimentsAssigned.Any(x => x.ExperimentId == experiment.Id))
                    {
                        //Specific conditions added for Spin The wheel
                        bool userAllowedForSpinTheWheel = experiment.Name == ExperimentType.SpinWheelRetention.ToString() ?
                         MoneyTransferService.IsUserAllowedForSpinTheWheel(user.CitizenId, _rewardServiceSettings, user.PhoneNumber) : false;

                        userAddedToAnyExperiment = true;

                        var experimentUser = new ExperimentUsers();
                        experimentUser.CardHolderId = user.CitizenId;
                        experimentUser.ExperimentId = experiment.Id;
                        experimentUser.NationalityCode = user.Nationality;
                        experimentUser.CreatedDate = DateTime.Now;

                        experimentUser.GroupCode = userAllowedForSpinTheWheel ? ExperimentGroupCodeType.B.ToString() : ExperimentGroupCodeType.A.ToString();

                        //add the data to the experimentAssigned
                        experimentUser.Experiment = experiment;

                        var getMultimedia = experiment.ExperimentGroupMultimedias.FirstOrDefault(f => f.GroupCode == experimentUser.GroupCode && f.NationalityCode == user.Nationality);
                        getMultimedia ??= experiment.ExperimentGroupMultimedias.FirstOrDefault(f => f.GroupCode == experimentUser.GroupCode && f.NationalityCode == null);

                        if (getMultimedia != null)
                        {
                            //Set the multimedia data
                            experimentUser.Experiment.ExperimentGroupMultimedias = new List<ExperimentGroupMultimedia> { getMultimedia };
                            user.LoginVideoUrl = getMultimedia?.Url;
                        }

                        experimentsAssigned.Add(experimentUser);

                        //Adding LoginUserVideo details
                        if (user.LoginVideoUrl != null && loginVideoExperimentDetails != null)
                        {
                            experimentUser = new ExperimentUsers();
                            experimentUser.CardHolderId = user.CitizenId;
                            experimentUser.ExperimentId = loginVideoExperimentDetails.Id;
                            experimentUser.NationalityCode = user.Nationality;
                            experimentUser.CreatedDate = DateTime.Now;

                            experimentUser.GroupCode = ExperimentGroupCodeType.B.ToString();

                            //add the data to the experimentAssigned
                            experimentUser.Experiment = loginVideoExperimentDetails;

                            //assign CTAs to the LoginExperiment
                            experimentUser.Experiment.ExperimentCtas = experiment.ExperimentCtas.Where(x => x.GroupCode == experimentUser.GroupCode).ToList();

                            experimentsAssigned.Add(experimentUser);
                        }
                    }
                }
            }

            // KycRmtStatus Experiment = rollout to all users
            var thisKycRmtStatusExperiment = experiments.FirstOrDefault(x => x.Name == "KycRmtStatus");
            if (thisKycRmtStatusExperiment != null && experimentsAssigned.Any(x => x.ExperimentId == thisKycRmtStatusExperiment.Id))
            {
                experimentsAssigned.FirstOrDefault(x => x.ExperimentId == thisKycRmtStatusExperiment.Id)
                .GroupCode = ExperimentGroupCodeType.A.ToString();
            }

            if (userAddedToAnyExperiment)
            {
                user.ExperimentUsers = experimentsAssigned.ToList();
            }

            user.IsInAppAuthUser = experimentsAssigned.Any(e => e.ExperimentId == (int)ExperimentType.InAppAuth && e.CardHolderId == user.CitizenId);


            #endregion Experiments


            var isC3PMockEnabled = await this._featureManager.IsEnabledAsync(FeatureFlags.C3P_MockExp);
            if (isC3PMockEnabled == true)
            {
                // If we have enabled mocking for C3Pay+, we need to check if this user is eligibile to
                // view C3Pay or not.
                // Check age of user, should be between 18 and 59.
                bool canView = CanViewC3PayPlus(user);
                if (canView == true)
                {
                    // Add a fake record to experiment users.
                    var hasC3PExpRecord = user.ExperimentUsers.Any(x => x.ExperimentId == (int)ExperimentType.C3PayPlus);
                    if (hasC3PExpRecord == false)
                    {
                        user.ExperimentUsers.Add(new ExperimentUsers()
                        {
                            ExperimentId = (int)ExperimentType.C3PayPlus,
                            GroupCode = "A",
                            Experiment = new Experiment()
                            {
                                Id = (int)ExperimentType.C3PayPlus,
                                Name = "C3PayPlus",
                                FeatureId = 18,
                                Feature = new Feature()
                                {
                                    Name = "C3PayPlus",
                                    Id = 18
                                }
                            }
                        });
                    }
                }
                else
                {
                    user.ExperimentUsers = user.ExperimentUsers.Where(x => x.ExperimentId != (int)ExperimentType.C3PayPlus).ToList();
                }
            }


            user.SuspiciousBeneficiaries = await _unitOfWork.MoneyTransferTransactions.GetSuspiciousBeneficiaries(user.Id, _moneyTransferServiceSettings.CheckMinSuspiciousDate);

            // Get User BirthPlace Details
            await UpdateUserBirthPlace(user);

            // Temporarily added this for rollout plan
            user.IsSmsPlusEnabled = await _lookupService.IsSmsPlusEnabled(user.CorporateId);

            return new ServiceResponse<UserProfile>(user);
        }

        private async Task UpdateUserBirthPlace(UserProfile user)
        {
            if (user.BirthProvinceId != null)
            {
                var provinceDetails = await _unitOfWork.ProvinceRepository.FirstOrDefaultAsync(i => i.Id == user.BirthProvinceId, include => include.Districts);

                if (provinceDetails != null)
                {
                    if (user.BirthDistrictId != null)
                    {
                        var district = provinceDetails.Districts.FirstOrDefault(c => c.Id == user.BirthDistrictId);
                        if (district != null && !string.IsNullOrEmpty(district.Name))
                            user.PlaceOfBirth += $"{district.Name}, {provinceDetails.Name}";
                    }
                    else
                    {
                        user.PlaceOfBirth = provinceDetails.Name;
                    }
                }

            }
        }

        private bool CanViewC3PayPlus(UserProfile user)
        {
            if (user.DateOfBirth.HasValue == false)
            {
                return false;
            }

            int age = DateTime.Now.Year - user.DateOfBirth.Value.Year;

            // Check if birthday has occurred this year.
            if (DateTime.Now.Month < user.DateOfBirth.Value.Month
                || (DateTime.Now.Month == user.DateOfBirth.Value.Month
                && DateTime.Now.Day < user.DateOfBirth.Value.Day))
            {
                age--;
            }

            if (age < 18 || age > 59)
            {
                return false;
            }

            if (user.IsVerified == false)
            {
                return false;
            }

            // KYC Checks.
            if (user.HasEmiratesIdIdentification == false)
            {
                return false;
            }

            return true;
        }

        public async Task<ServiceResponse<User>> GetUserByExternalId(int externalId)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.ExternalId == externalId && record.ApplicationId == MobileApplicationId.MySalary && !record.IsDeleted, record => record.CardHolder, record => record.Identifications);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFoundByExternalId, externalId));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<Guid>> GetUserIdByPhoneNumber(string phoneNumber)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.PhoneNumber == phoneNumber && record.ApplicationId == MobileApplicationId.C3Pay && !record.IsDeleted);
            if (user == null)
                return new ServiceResponse<Guid>(false, "");

            return new ServiceResponse<Guid>(user.Id);
        }

        public async Task<ServiceResponse<User>> GetUserByPhoneNumber(string phoneNumber)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.PhoneNumber == phoneNumber && record.ApplicationId == MobileApplicationId.C3Pay && !record.IsDeleted, record => record.CardHolder);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFoundByPhoneNumber, phoneNumber));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<User>> GetUserByCitizenId(string citizenId)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.CardHolderId == citizenId && record.ApplicationId == MobileApplicationId.C3Pay && !record.IsDeleted, user => user.CardHolder);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFoundByCitizenId, citizenId));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<User>> GetUserByCitizenIdForAllApplications(string citizenId)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.CardHolderId == citizenId && !record.IsDeleted, user => user.CardHolder);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFoundByCitizenId, citizenId));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<User>> GetUserByCardNumber(string cardNumber)
        {
            var user = await _unitOfWork.Users.SingleOrDefaultAsync(record => record.CardHolder.CardNumber == cardNumber && !record.IsDeleted);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFoundByCardNumber, cardNumber));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<User>> GetUserByC3RegistrationId(string c3RegistrationId)
        {
            var user = await _unitOfWork.Users.SingleOrDefaultAsync(record => record.CardHolder.C3RegistrationId == c3RegistrationId && !record.IsDeleted);
            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFoundByC3RegistrationId, c3RegistrationId));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<User>> GetUserByEmiratesId(string emiratesId)
        {
            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.CardHolder.EmiratesId == emiratesId && !record.IsDeleted, record => record.CardHolder);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFoundByEmiratesId, emiratesId));
            }

            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<CardNumberEligibilityResult>> GetCardNumberEligibility(string cardNumber)
        {
            CardNumberEligibilityResult eligibility = new CardNumberEligibilityResult();

            // Get cardholder reference ID (citizen ID) from PPS .
            var tryGetCardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest()
            {
                CardNumber = cardNumber
            });

            if (tryGetCardDetails.IsSuccessful == false || string.IsNullOrEmpty(tryGetCardDetails.Data?.CardholderRef))
            {
                eligibility.CardEligibilityResult = CardEligibilityResult.InvalidCard;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibility);
            }


            // Check if this card is already registered.
            var cardHolderReference = tryGetCardDetails.Data;

            var cardHolderId = cardHolderReference.CardholderRef;

            var userExists = await _unitOfWork.Users.Any(record => record.CardHolderId == cardHolderReference.CardholderRef && !record.IsDeleted);

            if (userExists == true)
            {
                eligibility.CardEligibilityResult = CardEligibilityResult.AlreadyRegistered;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibility);
            }

            // Get details about the cardholder.
            var tryGetCardHolder = await this._cardHolderService.GetCardHolderByCitizenId(cardHolderId);

            if (tryGetCardHolder.IsSuccessful == false)
            {
                eligibility.CardEligibilityResult = CardEligibilityResult.InvalidCard;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibility);
            }

            var cardHolder = tryGetCardHolder.Data;


            // Check if the card is valid.
            if (string.IsNullOrEmpty(cardHolder.CardSerialNumber))
            {
                eligibility.CardEligibilityResult = CardEligibilityResult.InvalidCard;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibility);
            }

            // Check if card is not blocked.
            var blocked = await this._cardHolderService.IsCardBlocked(cardHolder.CardSerialNumber);
            if (blocked.Data != null)
            {
                eligibility.CardEligibilityResult = CardEligibilityResult.Blocked;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibility);
            }


            // Check if card belongs to an exchange house.
            if (cardHolder.BelongsToExchangeHouse)
            {
                eligibility.CardEligibilityResult = CardEligibilityResult.InEligible;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibility);
            }


            eligibility.CardEligibilityResult = CardEligibilityResult.Eligible;
            eligibility.CardSerialNumber = cardHolder.CardSerialNumber;

            long cardSerialNumber = Convert.ToInt64(eligibility.CardSerialNumber);
            long firstBlackV1PlasticCardId = Convert.ToInt64(this._generalSettings.FirstBlackV1PlasticCardId);
            long firstBlackV2PlasticCardId = Convert.ToInt64(this._generalSettings.FirstBlackV2PlasticCardId);

            //3010688213 was the first serial number on the old black plastics (before this was blue).
            //3011215976 was the first serial number on the new black plastics (before was old black).
            if (cardSerialNumber < firstBlackV1PlasticCardId)
            {
                eligibility.CardPlasticType = CardPlasticType.Blue;
            }
            else if (cardSerialNumber >= firstBlackV1PlasticCardId && cardSerialNumber < firstBlackV2PlasticCardId)
            {
                eligibility.CardPlasticType = CardPlasticType.BlackV1;
            }
            else if (cardSerialNumber >= firstBlackV2PlasticCardId)
            {
                eligibility.CardPlasticType = CardPlasticType.BlackV2;
            }

            return new ServiceResponse<CardNumberEligibilityResult>(eligibility);
        }

        public async Task<ServiceResponse<PhoneEligibilityResult>> GetPhoneNumberEligibility(string phoneNumber, bool checkIdentity)
        {
            var secondDigit = phoneNumber[6];

            PhoneEligibilityResult eligibility;

            if (!_allowedSecondPhoneNumberDigits.Contains(secondDigit))
            {
                eligibility = PhoneEligibilityResult.InvalidPhoneNumber;
            }
            else
            {
                var userExists = await _unitOfWork.Users.Any(record => record.PhoneNumber == phoneNumber && !record.IsDeleted && record.ApplicationId == MobileApplicationId.C3Pay);
                if (userExists)
                {
                    eligibility = PhoneEligibilityResult.AlreadyRegistered;

                    var removeUserIfCardIsDeletedEnabled = await this._featureManager.IsEnabledAsync(FeatureFlags.RemoveUserIfCardIsDeleted);
                    if (removeUserIfCardIsDeletedEnabled)
                    {
                        var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.PhoneNumber == phoneNumber && !record.IsDeleted && record.ApplicationId == MobileApplicationId.C3Pay);

                        var tryGetCardDetails = await this._cardHolderService.GetCardHolderByCitizenId(user.CardHolderId);
                        if (!tryGetCardDetails.IsSuccessful && tryGetCardDetails.ErrorMessage == EnumUtility.GetDescriptionFromEnumValue(Enums.GetCardHolderDetails.ESGCHDS004))
                        {
                            await RemoveUserAccount(user.Id);
                            eligibility = PhoneEligibilityResult.Eligible;

                            this._logger.LogInformation("The card is deleted, phone number freed up");
                        }
                    }
                    else
                    {
                        this._logger.LogWarning($"{FeatureFlags.RemoveUserIfCardIsDeleted} flag is off");
                    }
                }
                else
                {
                    if (checkIdentity)
                    {
                        var identityExistsResult = await this._identityService.UserAccountExistsAsync(phoneNumber);

                        if (!identityExistsResult.IsSuccessful)
                        {
                            return new ServiceResponse<PhoneEligibilityResult>(false, identityExistsResult.ErrorMessage);
                        }

                        var identityExists = identityExistsResult.Data;

                        if (identityExists)
                        {
                            eligibility = PhoneEligibilityResult.AlreadyRegistered;
                        }
                        else
                        {
                            eligibility = PhoneEligibilityResult.Eligible;
                        }
                    }
                    else
                    {
                        eligibility = PhoneEligibilityResult.Eligible;
                    }
                }
            }

            return new ServiceResponse<PhoneEligibilityResult>(eligibility);
        }
        public async Task<ServiceResponse<string>> GetPhoneNumberUpdateEligibility(string citizenId, string phoneNumber)
        {
            PhoneEligibilityResult eligibility;

            var secondDigit = phoneNumber[6];

            if (!_allowedSecondPhoneNumberDigits.Contains(secondDigit) || phoneNumber.Length != 14 || !phoneNumber.StartsWith("009715"))
            {
                eligibility = PhoneEligibilityResult.InvalidPhoneNumber;
            }
            else
            {
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.PhoneNumber == phoneNumber && record.ApplicationId == MobileApplicationId.C3Pay && !record.IsDeleted);

                if (user == null)
                {
                    eligibility = PhoneEligibilityResult.Eligible;
                }
                else
                {
                    eligibility = user.CardHolderId == citizenId ? PhoneEligibilityResult.MatchesUserPhone : PhoneEligibilityResult.AlreadyRegistered;
                }
            }

            return new ServiceResponse<string>(eligibility.ToString());
        }

        public async Task<ServiceResponse<IEnumerable<SecretAnswer>>> GetSecretAnswers(Guid userId)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == userId, record => record.SecretAnswers);

            if (user == null)
            {
                return new ServiceResponse<IEnumerable<SecretAnswer>>(false, string.Format(ConstantParam.UserNotFound, userId));
            }

            return new ServiceResponse<IEnumerable<SecretAnswer>>(user.SecretAnswers);
        }

        /// <summary>
        /// User Card Balance
        /// </summary>
        /// <param name="UserId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<UserCardBalance>> GetUserCardBalance(User user, CancellationToken cancellationToken = default(CancellationToken))
        {
            string cardPanNumber = string.Empty;
            string cardSerialNumber = string.Empty;
            double CardBalance = 0;
            string Currency = ConstantParam.DefaultCurrency;

            //Read Card information            
            if (user.CardHolder != null)
            {
                cardPanNumber = user.CardHolder.CardNumber;
                cardSerialNumber = user.CardHolder.CardSerialNumber;
            }

            if (string.IsNullOrEmpty(cardPanNumber))
            {
                return new ServiceResponse<UserCardBalance>(false, ConstantParam.InvalidCardNumber);
            }
            else if (string.IsNullOrEmpty(cardSerialNumber))
            {
                return new ServiceResponse<UserCardBalance>(false, ConstantParam.InvalidCardSerialNumber);
            }

            if (!string.IsNullOrEmpty(cardPanNumber) && !string.IsNullOrEmpty(cardSerialNumber))
            {
                cardPanNumber = cardPanNumber.Substring(cardPanNumber.Length - 4, 4);

                var balResult = await _ppsWebAuthervice.GetCardBalance(new BalanceRequest()
                {
                    CardPanNumber = cardPanNumber,
                    CardSerialNumber = cardSerialNumber,
                    Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                    Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                    ReferenceNumber = TypeUtility.GetReferenceNumber(BaseEnums.TransactionPrefix.BAL.ToString(), 12)
                }, cancellationToken);

                if (balResult.IsSuccessful && balResult.Data.StatusCode == "00")
                {
                    CardBalance = TypeUtility.GetDoubleFromString(balResult.Data.EndBalanace.Amt) / 100;
                    Currency = balResult.Data.EndBalanace.Currency;
                }
                else
                {
                    if (balResult.Data != null)
                        return new ServiceResponse<UserCardBalance>(false, balResult.Data.Message);
                    else
                        return new ServiceResponse<UserCardBalance>(false, ConstantParam.UnableToConnectToPPSWebAuthService);
                }
            }

            var UserBalance = new UserCardBalance()
            {
                Currency = Currency,
                BalanceAmount = CardBalance
            };

            return new ServiceResponse<UserCardBalance>(UserBalance);
        }

        public async Task<ServiceResponse<bool>> UserHasPendingIdentifications(Guid userId)
        {
            var userHasPendingIdentifications = await this._unitOfWork.Identifications
                .Any(record => record.UserId == userId && record.VerificationStatus == BaseEnums.IdentificationVerificationStatus.Pending);

            return new ServiceResponse<bool>(userHasPendingIdentifications);
        }

        public async Task<ServiceResponse> UpdateUserDeviceToken(User user, string deviceToken)
        {
            this._logger.LogDebug(ConstantParam.UpdatingUserDeviceToken, user.Id, user.DeviceToken ?? string.Empty, deviceToken);

            user.DeviceToken = deviceToken;

            await this._unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdateUserDeviceId(Guid userId, string deviceId, string deviceModel)
        {
            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(u => u.Id == userId && !u.IsDeleted, u => u.Devices);

            if (user == null)
            {
                return new ServiceResponse<User>(false, string.Format(ConstantParam.UserNotFound, userId));
            }

            if (user.Devices == null)
            {
                user.Devices = new List<UserDevice>();
            }

            var userLastDevice = user.Devices.OrderByDescending(d => d.CreatedDate).FirstOrDefault();

            if (userLastDevice == null || userLastDevice.DeviceId != deviceId)
            {
                user.Devices.Add(new UserDevice()
                {
                    DeviceId = deviceId,
                    Model = deviceModel
                });

                await this._unitOfWork.CommitAsync();
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ResetPasswordResetFlag(User user)
        {
            user.RequiresPasswordReset = false;

            await this._unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        public async Task<UserBaseDto<EmailUpdateDto>> UpdateUserEmail(User user, string email, bool emailConsent = false)
        {
            var username = user.PhoneNumber;

            this._logger.LogDebug(ConstantParam.UpdatingUserEmail, username, user.Id, user.Email);

            var updateEmailResult = await this._identityService.ChangeEmailAsync(username, email);

            if (!updateEmailResult.IsSuccessful)
            {
                return new UserBaseDto<EmailUpdateDto>()
                {
                    IsSuccessful = false,
                    ErrorMessage = updateEmailResult.ErrorMessage
                };
            }

            user.Email = email;

            await this._unitOfWork.CommitAsync();

            if (emailConsent == true)
            {
                // Update consent details.
                user.Email = email;
                user.EmailConsent = true;
                user.EmailConsentDate = DateTime.Now;
                user.ReceiveAlertsVia = AlertChannelType.Email;

                await this._unitOfWork.CommitAsync();

                return new UserBaseDto<EmailUpdateDto>()
                {
                    IsSuccessful = true,
                    Data = new EmailUpdateDto()
                    {
                        Updated = true,
                        EmailConsent = user.EmailConsent.HasValue && user.EmailConsent.Value == true ? true : false,
                        EmailConsentDate = user.EmailConsentDate
                    }
                };
            }

            return new UserBaseDto<EmailUpdateDto>() { IsSuccessful = true };
        }


        public async Task<ServiceResponse> UpdatePhoneNumber(User user, string phoneNumber)
        {
            if (user.PhoneNumber.IsUaePhoneNumber() == false && phoneNumber.IsUaePhoneNumber() == true)
            {
                user.IsInternationalPhoneNumberUser = false;
            }

            user.PhoneNumber = phoneNumber;
            await AddOutboxMessage(user, OutboxMessageTypeEnum.UserPhoneNumberUpdatedEvent.ToString());
            await this._unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdatePhoneNumberV2(User user, string oldPhoneNumber, string newPhoneNumber, string source)
        {
            user.PhoneNumber = newPhoneNumber;

            if (oldPhoneNumber.IsUaePhoneNumber() == false && newPhoneNumber.IsUaePhoneNumber() == true)
            {
                user.IsInternationalPhoneNumberUser = false;
            }

            user.AddPhoneNumberUpdateLog(oldPhoneNumber, newPhoneNumber, source);
            await AddOutboxMessage(user, OutboxMessageTypeEnum.UserPhoneNumberUpdatedEvent.ToString());
            await this._unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> UpdateSecretAnswers(Guid userId, List<SecretAnswer> secretAnswers)
        {
            this._logger.LogDebug(ConstantParam.UpdatingUserSecretAnswers, userId);

            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == userId, record => record.SecretAnswers);

            if (user == null)
            {
                var error = string.Format(ConstantParam.UserNotFound, userId);

                this._logger.LogWarning(ConstantParam.UpdatingUserSecretAnswersFailed, userId, error);

                return new ServiceResponse(error);
            }

            this._unitOfWork.SecretAnswers.RemoveRange(user.SecretAnswers);

            user.SecretAnswers = secretAnswers;

            await this._unitOfWork.CommitAsync();

            return new ServiceResponse();

        }

        /// <summary>
        /// Get Comments by UserId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<IEnumerable<VerificationComment>>> GetUserComments(Guid id)
        {
            var comments = await _unitOfWork.VerificationComments.FindAsync(x => x.UserId == id && !x.IsDeleted, x => x.User);
            return new ServiceResponse<IEnumerable<VerificationComment>>(comments.OrderByDescending(x => x.CreatedDate));
        }

        /// <summary>
        /// Add Comments
        /// </summary>
        /// <param name="addUserComment"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> AddUserComment(AddUserComment addUserComment)
        {
            var comment = new VerificationComment()
            {
                Id = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                UserId = addUserComment.UserId,
                PortalUserId = addUserComment.AdminId,
                PortalEmailId = addUserComment.PortalEmailId,
                Comment = addUserComment.Comment
            };
            await _unitOfWork.VerificationComments.AddAsync(comment);
            await _unitOfWork.CommitAsync();
            return new ServiceResponse();
        }

        public async Task<ServiceResponse<string>> GetUserKYCRemarks(Guid id)
        {
            var kycRemarks = await _unitOfWork.Users.FindAsync(x => x.Id == id, x => x.Identifications);
            if (kycRemarks != null && kycRemarks.Count > 0 &&
                kycRemarks.First().Identifications.Count > 0)
            {
                return new ServiceResponse<string>(kycRemarks.First().Identifications.OrderByDescending(x => x.CreatedDate).First().PostRemarks);
            }
            return new ServiceResponse<string>(string.Empty);
        }

        public async Task<ServiceResponse<IdentificationStatus>> GetUserEmiratesIdStatus(bool userIsVerified, string emiratesId, DateTime? emiratesIdExpiryDate, bool hasApprovedUpdates, bool hasPendingUpdates, bool hasApprovedPassportUpdated)
        {
            IdentificationStatus status;
            var todaysDate = DateTime.Now.Date;

            if (!hasApprovedUpdates)
            {
                if (hasPendingUpdates)
                {
                    status = IdentificationStatus.Validating;
                }
                else
                {
                    var idNumberIsValid = this.ValidateEmiratesId(emiratesId).Data;

                    if (userIsVerified && emiratesIdExpiryDate.HasValue && idNumberIsValid && !hasApprovedPassportUpdated)
                    {
                        status = emiratesIdExpiryDate.Value < todaysDate ? IdentificationStatus.Expired : emiratesIdExpiryDate.Value > todaysDate.AddDays(60) ? IdentificationStatus.Valid : status = IdentificationStatus.ValidUpdatable;
                    }
                    else
                    {
                        status = IdentificationStatus.Missing;
                    }
                }
            }
            else
            {
                var enableInGrace = await _featureManager.IsEnabledAsync(FeatureFlags.KycEnableInGraceStatus);

                if (enableInGrace && emiratesIdExpiryDate.HasValue && todaysDate > emiratesIdExpiryDate && (todaysDate - emiratesIdExpiryDate.Value).TotalDays <= 90)
                {
                    status = IdentificationStatus.InGrace;
                }
                else if (emiratesIdExpiryDate.HasValue && emiratesIdExpiryDate.Value < todaysDate)
                {
                    status = IdentificationStatus.Expired;
                }
                else
                {
                    var idNumberIsValid = this.ValidateEmiratesId(emiratesId).Data;

                    if (idNumberIsValid && emiratesIdExpiryDate.HasValue)
                    {
                        status = emiratesIdExpiryDate.Value > todaysDate.AddDays(60) ? IdentificationStatus.Valid : IdentificationStatus.ValidUpdatable;
                    }
                    else
                    {
                        status = IdentificationStatus.Missing;
                    }
                }
            }

            return new ServiceResponse<IdentificationStatus>(status);
        }

        public ServiceResponse<IdentificationStatus> GetUserPassportStatus(bool hasApprovedUpdates, bool hasPendingUpdates)
        {
            IdentificationStatus status;

            if (!hasApprovedUpdates)
            {
                status = hasPendingUpdates ? IdentificationStatus.Validating : IdentificationStatus.Missing;
            }
            else
            {
                status = IdentificationStatus.Valid;
            }

            return new ServiceResponse<IdentificationStatus>(status);
        }


        public ServiceResponse<bool> ValidateEmiratesId(string emiratesId)
        {
            bool emiratesIdIsValid = true;

            if (emiratesId is null)
            {
                return new ServiceResponse<bool>(false);
            }

            emiratesId = emiratesId.Trim();

            if (string.IsNullOrEmpty(emiratesId) || !emiratesId.StartsWith("784") || emiratesId.Length != 15 || !Regex.IsMatch(emiratesId, @"^[0-9]*$"))
            {
                emiratesIdIsValid = false;
            }

            for (int i = 1; i < 10; i++)
            {
                if (emiratesId.Contains(string.Concat(i, i, i, i, i, i, i)))
                {
                    emiratesIdIsValid = false;
                }
            }

            return new ServiceResponse<bool>(emiratesIdIsValid);
        }

        private string GenerateDigitPasswordWithSingleAlphabet(int length)
        {
            var password = new StringBuilder();
            var random = new Random();
            char[] charArray = "qwertyuplkjhgfdsazxcvbnm".ToCharArray();
            var chartakeindex = random.Next(0, charArray.Length - 1);
            var charindex = random.Next(0, length - 1);
            if (length > 10)
                length = 10;
            for (var i = 0; i <= length - 2; i++)
            {
                password.Append(random.Next(0, 9));
            }
            password.Insert(charindex, charArray[chartakeindex]);
            return password.ToString();
        }

        public async Task<ServiceResponse<bool>> CheckCardOwnership(Guid userId, string cardHolderRef)
        {
            cardHolderRef = cardHolderRef.Trim();
            var owned = await this._unitOfWork.Users.Any(u => u.Id == userId && u.CardHolderId == cardHolderRef);
            return new ServiceResponse<bool>(owned);
        }

        public async Task<ServiceResponse> DisableAtmPinPrompt(User user)
        {
            if (user.Preferences is null)
            {
                user.Preferences = new UserPreferences();
            }

            user.Preferences.ATMPinPopupEnabled = false;

            await this._unitOfWork.CommitAsync();
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> SetATMPinBlockEndDate(User user, DateTime date)
        {
            user.ATMPinBlockEndDate = date;
            await this._unitOfWork.CommitAsync();
            return new ServiceResponse();
        }

        public async Task<ServiceResponse<bool>> IncrementAutoUnblockWrongAttemptsCount(User user)
        {
            bool maxAttemptsReached = false;

            if (user.AutoUnblock is null)
            {
                user.AutoUnblock = new UserAutoUnblock();
            }

            user.AutoUnblock.WrongAttemptsCount++;
            user.AutoUnblock.LastWrongAttemptDate = DateTime.Now;

            if (user.AutoUnblock.WrongAttemptsCount >= this._generalSettings.AutoUnblockMaxAttemptCount)
            {
                maxAttemptsReached = true;
            }

            await this._unitOfWork.CommitAsync();

            return new ServiceResponse<bool>(maxAttemptsReached);
        }

        public async Task<ServiceResponse> ResetAutoUnblockWrongAttemptsCount(User user)
        {
            if (user.AutoUnblock is null)
            {
                user.AutoUnblock = new UserAutoUnblock();
            }

            user.AutoUnblock.WrongAttemptsCount = 0;

            await this._unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ProcessUsersSalaryEvents(PushSalaryProcessedEventsDto salaryProcessedEvents)
        {
            var salaryEvents = new List<Tuple<string, SalaryProcessedEvent>>();
            var cardHoldersIds = salaryProcessedEvents.SalariesProcessed.Select(e => e.CitizenId);
            var users = await this._unitOfWork.Users.FindAsync(u => cardHoldersIds.Contains(u.CardHolderId) && !u.IsDeleted);

            var enableFreeTransferExpiry = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableFreeTransferExpiry);
            var goldIncentiveAcquisitionEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableGoldIncentiveAcquisition);
            if (enableFreeTransferExpiry || goldIncentiveAcquisitionEnabled)
            {
                List<User> usersToUpdate = new List<User>();
                foreach (var item in users.Where(x => !x.IsFirstSalaryProcessed))
                {
                    item.IsFirstSalaryProcessed = true;

                    if (enableFreeTransferExpiry && item.FreeTransferExpiryDate == null)
                    {
                        var experimentAssigned = await _cacheService.GetRecordAsync<bool>(string.Concat(_freeTransferCacheKeyPrefix, item.Id));
                        if (item.MoneyTransferProfileStatus == MoneyTransferProfileStatus.Created && experimentAssigned)
                        {
                            item.FreeTransferExpiryDate = DateTime.Now.AddDays(7);
                        }
                    }
                    if (goldIncentiveAcquisitionEnabled && item.MtGoldIncentiveExpiryDate == null)
                    {
                        var experimentAssigned = await _cacheService.GetRecordAsync<bool>(string.Concat(_mTGoldIncentiveCacheKeyPrefix, item.Id));
                        if (item.MoneyTransferProfileStatus == MoneyTransferProfileStatus.Created && experimentAssigned)
                            item.MtGoldIncentiveExpiryDate = DateTime.Now.AddDays(7);
                    }

                    usersToUpdate.Add(item);
                }

                await _unitOfWork.Users.BulkUpdateAsync(usersToUpdate);
                await _unitOfWork.CommitAsync();
            }

            foreach (var salaryProcessed in salaryProcessedEvents.SalariesProcessed)
            {
                var user = users.FirstOrDefault(u => u.CardHolderId == salaryProcessed.CitizenId);
                if (user != null)
                {
                    var salaryEvent = new SalaryProcessedEvent()
                    {
                        Amount = salaryProcessed.Amount,
                        ProcessedDate = salaryProcessed.ProcessedDate
                    };

                    salaryEvents.Add(new Tuple<string, SalaryProcessedEvent>(salaryProcessed.CitizenId, salaryEvent));
                }
            }

            await _analyticsPublisherService.PublishSalaryProcessedEvent(salaryEvents);

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<string>> GetOrGenerateUserReferralCode(Guid userId)
        {
            var userResult = await this.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return new ServiceResponse<string>(false, userResult.ErrorMessage);
            }

            var user = userResult.Data;

            if (!string.IsNullOrEmpty(user.ReferralCode))
            {
                return new ServiceResponse<string>(user.ReferralCode);
            }

            var referralCode = GenerateReferralCode();

            while (await this._unitOfWork.Users.Any(u => u.ReferralCode == referralCode))
            {
                referralCode = GenerateReferralCode();
            }

            user.ReferralCode = referralCode;

            await this._unitOfWork.CommitAsync();

            return new ServiceResponse<string>(referralCode);
        }

        private string GenerateReferralCode()
        {
            var chars = "abcdefghjklmnpqrstuvwxyz";

            var referralCode = new StringBuilder();

            var stringChars = new char[3];

            var random = new Random();

            for (int j = 0; j < stringChars.Length; j++)
            {
                stringChars[j] = chars[random.Next(chars.Length)];
            }

            referralCode.Append(new String(stringChars));

            var numberRandom = new Random();

            referralCode.Append(numberRandom.Next(1, 9));

            referralCode.Append(numberRandom.Next(1, 9));

            return referralCode.ToString();
        }

        public async Task<ServiceResponse> ActivateCard(Guid userId, Guid? portalUserId = null, string portalEmailId = null)
        {
            var userResult = await this.GetUserById(userId);

            if (!userResult.IsSuccessful)
            {
                return new ServiceResponse(false, userResult.ErrorMessage);
            }

            if (userResult.Data.CardHolder != null && !string.IsNullOrEmpty(userResult.Data.CardHolder.CardSerialNumber))
            {
                if (userResult.Data.CardHolder.C3RegistrationId != null &&
                    await _featureManager.IsEnabledAsync(FeatureFlags.SanctionScreeningValidateScreeningStatus))
                {
                    var validateSanctionScreeningResult =
                        await _sanctionScreeningService.ValidateUserSanctionScreening(userResult.Data.CardHolder.C3RegistrationId);

                    if (validateSanctionScreeningResult.IsValid == false)
                    {
                        return new ServiceResponse(IdentificationVerificationResult.SanctionScreeningInvalid.ToString());
                    }
                }
                await this._ppsService.ActivateCard(userResult.Data.CardHolder.CardSerialNumber);

                if (portalUserId != null)
                {
                    var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, userId, ConstantParam.AuditActivateCard, userResult.Data.PhoneNumber, null);
                }

                return new ServiceResponse();
            }
            else
            {
                return new ServiceResponse(false, ConstantParam.NotExistsCardHolder);
            }
        }

        public async Task<ServiceResponse<List<string>>> GetActiveUsersDeviceTokensByCorporateId(string corporateId)
        {
            var deviceTokens = await this._unitOfWork.Users.GetActiveUsersDeviceTokensByCorporateId(corporateId);

            return new ServiceResponse<List<string>>(deviceTokens);
        }

        private async Task<UserProfile> LoadUserVideos(UserProfile user)
        {
            var nationality = user.Nationality != null && Enum.IsDefined(typeof(MultimediaCountry), user.Nationality) ? user.Nationality : MultimediaCountry.IND.ToString();

            var multimediaByNationality = (await _lookupService.GetMultimediaResources(nationality, type: MultimediaType.Video)).Data;
            if (multimediaByNationality != null && multimediaByNationality.Count > 0)
            {
                var moneyTransfer = multimediaByNationality.FirstOrDefault(x => x.FeatureId == (int)FeatureType.MoneyTransfer);
                var referralProgram = multimediaByNationality.FirstOrDefault(x => x.FeatureId == (int)FeatureType.ReferralProgram);
                var billPayment = multimediaByNationality.FirstOrDefault(x => x.FeatureId == (int)FeatureType.BillPayments);
                var ehRatesBetter = multimediaByNationality.FirstOrDefault(x => x.FeatureId == (int)FeatureType.EhRatesAreBetter);
                var loginVideo = multimediaByNationality.FirstOrDefault(x => x.FeatureId == (int)FeatureType.Login);

                user.MoneyTransferVideoURL = moneyTransfer?.Url;
                user.MoneyTransferVideoThumbnailURL = moneyTransfer?.ThumbnailUrl;

                user.ReferralProgramVideoURL = referralProgram?.Url;
                user.ReferralProgramVideoThumbnailURL = referralProgram?.ThumbnailUrl;

                user.BillPaymentVideoURL = billPayment?.Url;
                user.BillPaymentVideoThumbnailURL = billPayment?.ThumbnailUrl;

                user.EhRatesAreBetterThumbnailURL = ehRatesBetter?.ThumbnailUrl;
                user.EhRatesAreBetterVideoURL = ehRatesBetter?.Url;

                user.LoginVideoUrl = loginVideo?.Url;
            }

            //If bill payment is null, add arabic bill payment video
            if (user.BillPaymentVideoURL == null)
            {
                var billPayment = (await _lookupService.GetMultimediaResources("ARE", type: MultimediaType.Video, feature: (int)FeatureType.BillPayments)).Data.FirstOrDefault();
                if (billPayment != null)
                {
                    user.BillPaymentVideoURL = billPayment.Url;
                    user.BillPaymentVideoThumbnailURL = billPayment.ThumbnailUrl;
                }
            }

            user.AutoplayMoneyTransferVideo = false;

            if (!string.IsNullOrWhiteSpace(_multimediaSettings.AutoPlayMoneyTransferVideoCorporateIds))
            {
                List<string> corporateIds = _multimediaSettings.AutoPlayMoneyTransferVideoCorporateIds.Split(',').Select(a => a).ToList();

                if (corporateIds.Contains(user.CorporateId))
                {
                    user.AutoplayMoneyTransferVideo = true;
                }
            }

            if (_multimediaSettings.AutoPlayMoneyTransferVideoForAll)
                user.AutoplayMoneyTransferVideo = true;

            return user;
        }

        public async Task<ServiceResponse> RemoveIdenityIfUserDoesntExist(string phoneNumber)
        {
            var userExists = await _unitOfWork.Users.Any(
                u => !u.IsDeleted && u.ApplicationId == MobileApplicationId.C3Pay
                && u.PhoneNumber == phoneNumber);

            if (!userExists)
            {
                await this._identityService.DeleteUserAccountAsync(phoneNumber);
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<List<User>>> GetUsersByIds(IList<Guid> userIds)
        {
            var responseUser = await this._unitOfWorkReadonly.Users.SearchUsers(userIds);
            return new ServiceResponse<List<User>>(responseUser);
        }

        public async Task<ServiceResponse<User>> GetUserDetails(SalaryAdvanceParameter salaryAdvanceParameter)
        {
            var filters = UserFilter.GetSalaryAdvanceFilter(salaryAdvanceParameter);

            var user = await this._unitOfWork.Users.SearchSalaryAdvance(filters);

            if (user == null)
            {
                return new ServiceResponse<User>(null);
            }
            return new ServiceResponse<User>(user);
        }

        public async Task<ServiceResponse<User>> FindUser(Guid userId)
        {
            var user = await this._unitOfWork.Users.FirstOrDefaultAsync(u => u.Id == userId && u.IsDeleted == false && u.IsBlocked == false, u => u.CardHolder);
            if (user == null)
            {
                return new ServiceResponse<User>(false, ConstantParam.UserNotFound);
            }
            else
            {
                return new ServiceResponse<User>(user);
            }
        }

        public async Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsersFullKYC(SearchUserParameters searchUserParameters)
        {
            if (ValidateSearch(searchUserParameters) || !string.IsNullOrEmpty(searchUserParameters.RegistrationType))
            {
                var responseUser = await this._unitOfWorkReadonly.Users.SearchVerificationUsersFullKYC(searchUserParameters);
                return new ServiceResponse<Tuple<List<VerificationUser>, int>>(responseUser);
            }
            return new ServiceResponse<Tuple<List<VerificationUser>, int>>(new Tuple<List<VerificationUser>, int>(new List<VerificationUser>(), 0));
        }

        public async Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsersPartialKYC(SearchUserParameters searchUserParameters)
        {
            if (ValidateSearch(searchUserParameters) || !string.IsNullOrEmpty(searchUserParameters.RegistrationType))
            {
                var responseUser = await this._unitOfWorkReadonly.Users.SearchVerificationUsersPartialKYC(searchUserParameters);
                return new ServiceResponse<Tuple<List<VerificationUser>, int>>(responseUser);
            }
            return new ServiceResponse<Tuple<List<VerificationUser>, int>>(new Tuple<List<VerificationUser>, int>(new List<VerificationUser>(), 0));
        }

        public async Task<ServiceResponse<Tuple<List<VerificationUser>, int>>> SearchVerificationUsersNoKYC(SearchUserParameters searchUserParameters)
        {
            if (ValidateSearch(searchUserParameters) || !string.IsNullOrEmpty(searchUserParameters.RegistrationType))
            {
                var responseUser = await this._unitOfWorkReadonly.Users.SearchVerificationUsersNoKYC(searchUserParameters);
                return new ServiceResponse<Tuple<List<VerificationUser>, int>>(responseUser);
            }
            return new ServiceResponse<Tuple<List<VerificationUser>, int>>(new Tuple<List<VerificationUser>, int>(new List<VerificationUser>(), 0));
        }

        private bool ValidateSearch(SearchUserParameters searchUserParameters)
        {
            var isValid = false;
            var itemToCheck = new List<string> {  searchUserParameters.Name,
                                searchUserParameters.Mobile ,
                                searchUserParameters.BranchId,
                                searchUserParameters.EmiratesId,
                                searchUserParameters.CardSerialNumber,
                                searchUserParameters.CitizenId}.ToList();
            foreach (var item in itemToCheck)
            {
                if (!string.IsNullOrEmpty(item) && item.Length >= _minSearchLength)
                {
                    isValid = true;
                    break;
                }
            }
            return isValid;
        }
        public async Task<ServiceResponse<CardNumberEligibilityResult>> GetCardAndCvc2Eligibility(string cardNumber, string cvc2)
        {
            var eligibilityResult = new CardNumberEligibilityResult();

            if (string.IsNullOrEmpty(cvc2))
            {
                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            var tryGetCardDetails = await this._esmoWebService.GetCardDetails(new GetCardDetailsRequest()
            {
                CardNumber = cardNumber
            });

            if (tryGetCardDetails.IsSuccessful == false || string.IsNullOrEmpty(tryGetCardDetails.Data?.CardholderRef))
            {
                if (tryGetCardDetails.ErrorMessage != CardEligibilityResult.InvalidCard.ToString())
                {
                    return new ServiceResponse<CardNumberEligibilityResult>(false, tryGetCardDetails.ErrorMessage);
                }

                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            var cardHolderReference = tryGetCardDetails.Data;

            var cardHolderId = cardHolderReference.CardholderRef;

            var userExists = await _unitOfWork.Users.Any(record => record.CardHolderId == cardHolderReference.CardholderRef && !record.IsDeleted);

            if (userExists)
            {
                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            // Get details about the cardholder.
            var tryGetCardHolder = await this._cardHolderService.GetCardHolderByCitizenId(cardHolderId);

            if (!tryGetCardHolder.IsSuccessful)
            {
                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            var cardHolder = tryGetCardHolder.Data;

            var allowDromantCardsRegistration = await this._featureManager.IsEnabledAsync(FeatureFlags.AllowDromantCardsRegistration);

            if (cardHolder.EmployeeStatus == EmployeeStatus.Dormant.ToString() && !allowDromantCardsRegistration)
            {
                _logger.LogInformation("Blocked dormant card from being registered: {cardSerialNumber}", cardHolder.CardSerialNumber);
                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            // Check if the card is valid.
            if (string.IsNullOrEmpty(cardHolder.CardSerialNumber))
            {
                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            // Check if card is not blocked.
            var blocked = await this._cardHolderService.IsCardBlocked(cardHolder.CardSerialNumber);

            if (blocked.Data != null)
            {
                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            //check if card belongs to exchange house that is not listed into partners
            var partners = await _partnerCorporateService.GetAllPartners();
            var directPartner = partners.FirstOrDefault(x => x.Code == PartnerType.DirectClient.ToString());
            if (directPartner != null)
            {
                eligibilityResult.TermsAndConditionsUrl = directPartner.TermsAndConditionsUrl;
            }

            if (cardHolder.BelongsToExchangeHouse)
            {
                var partnerCorporate = (await _partnerCorporateService.GetPartnerCorporates()).FirstOrDefault(x => x.CorporateId == cardHolder.CorporateId);
                if (partnerCorporate == null)
                {
                    eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                    return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
                }
                else
                {
                    eligibilityResult.TermsAndConditionsUrl = partnerCorporate.Partner.TermsAndConditionsUrl;
                }
            }

            var kycBlocked = (await _unitOfWork.MissingKycCardholders.FindAsync(u => u.CitizenId == cardHolder.Id &&
                            (u.Status == KycStatus.NotSubmittedKycBlocked || u.Status == KycStatus.Blocked))).FirstOrDefault();
            if (kycBlocked != null)
            {
                var status = await _ppsService.GetCardStatus(cardHolder.CardSerialNumber);
                if (status.IsSuccessful && status.Data == "Blocked")
                {
                    // For missing KYC cardholder, we need to first unblock the card so we can verify the CVC.
                    var unblockedKycCardholder = await _ppsService.UnblockCard(cardHolder.CardSerialNumber);
                    if (unblockedKycCardholder.IsSuccessful == false)
                    {
                        // Can't unblock card.
                        _logger.LogWarning($"Can't unblock card. Error: {unblockedKycCardholder.ErrorMessage}");

                        eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                        return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
                    }
                }
                else
                {
                    kycBlocked = null;
                }
            }

            var isCvc2Valid = await _esmoWebService.VerifyCvc2(new VerifyCvc2Request()
            {
                CardSerialNumber = cardHolder.CardSerialNumber,
                Cvc2 = cvc2
            });

            // Block card again.
            if (kycBlocked != null)
            {
                var blockedKycCardholder = await _ppsService.BlockCard(cardHolder.CardSerialNumber);
                if (blockedKycCardholder.IsSuccessful == false)
                {
                    // Can't unblock card.
                    _logger.LogWarning($"Can't block card. Error: {blockedKycCardholder.ErrorMessage}");

                    eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;
                    return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
                }
            }

            // Validate CVC2
            if (isCvc2Valid.IsSuccessful == false)
            {
                Enum.TryParse(isCvc2Valid.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                if (atmPinError == Enums.AtmPinErrors.MAX_PIN_TRIES_EXCEEDED)
                {
                    await this._cardHolderService.BlockCard(cardHolder.CardSerialNumber);
                }

                eligibilityResult.CardEligibilityResult = CardEligibilityResult.InvalidCombination;

                return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
            }

            eligibilityResult.CardEligibilityResult = CardEligibilityResult.Eligible;

            return new ServiceResponse<CardNumberEligibilityResult>(eligibilityResult);
        }

        public async Task<ServiceResponse<bool>> IsVerifiedWithEmiratesId(Guid userId)
        {
            bool isVerifiedWithEId = await _unitOfWork.Identifications.Any(a => a.UserId == userId && a.Type == IdentificationType.EmiratesId);
            return new ServiceResponse<bool>(isVerifiedWithEId);
        }

        public async Task<ServiceResponse<List<User>>> GetUsersByCitizenIds(List<string> citizenIds)
        {
            var users = await _unitOfWork.Users.FindAsync(record => citizenIds.Contains(record.CardHolderId) && !record.IsDeleted, record => record.CardHolder);

            return new ServiceResponse<List<User>>(users.ToList());
        }

        public async Task<ServiceResponse> UpdateUserDevice(string phoneNumber, string model, string uniqueDeviceId, CancellationToken cancellationToken)
        {
            var user = await this._unitOfWork.Users.GetUserInfoWithDevicesAsync(phoneNumber, cancellationToken);
            if (user == null)
                return new ServiceResponse<User>(false, ConstantParam.InvalidUser);

            var validateError = user.ValidateDeviceId(uniqueDeviceId, _generalSettings);
            if (validateError != ConstantParam.ValidDevice)
                return new ServiceResponse<User>(false, validateError);

            // Check and Add Device to Device List
            var result = user.AddDevice(uniqueDeviceId, model, _generalSettings);

            // Final Unit of Work - Commit
            await _unitOfWork.CommitAsync();

            //Send New Device SMS Notification for subsequent device bind
            var bindedDevices = user.Devices.Where(d => !string.IsNullOrEmpty(d.UniqueDeviceId)).ToList();

            if (bindedDevices.Count > 1)
                await _textMessageSenderService.SendNewDeviceAddedMessage(phoneNumber, user.IsVerified);

            return new ServiceResponse<User>();
        }

        public async Task<ServiceResponse<(DeviceBindingStatus, string)>> CheckDeviceIsValidOrNot(string phoneNumber, string uniqueDeviceId, CancellationToken cancellationToken)
        {
            var enableMFAGetDeviceExistsV2 = await _featureManager.IsEnabledAsync(FeatureFlags.MFAGetDeviceExistsV2);

            var deviceBindingResponse = enableMFAGetDeviceExistsV2
                ? await _unitOfWork.Users.GetDeviceExistsStatusAsyncV2(phoneNumber, uniqueDeviceId, cancellationToken)
                : await _unitOfWork.Users.GetDeviceExistsStatusAsync(phoneNumber, uniqueDeviceId, cancellationToken);

            if (deviceBindingResponse == DeviceBindingStatus.VALID)
            {
                var userCache = await RefetchUserCache(phoneNumber);
                return new ServiceResponse<(DeviceBindingStatus, string)>((deviceBindingResponse, userCache.DeviceToken));
            }
            else
                await ClearCache(phoneNumber);
            return new ServiceResponse<(DeviceBindingStatus, string)>((deviceBindingResponse, string.Empty));
        }

        public async Task<ServiceResponse> ValidateUpdationKYCExpiry()
        {
            var enableBlockingExchangeHouseUsers = await _featureManager.IsEnabledAsync(FeatureFlags.KycEnableBlockingOfExchangeHouseUsers);

            await ProcessExpiryEventsAsync(enableBlockingExchangeHouseUsers);
            await ProcessNonRegisteredExpiredIdentifications();
            return new ServiceResponse();
        }

        public async Task<ServiceResponse> ValidateAdditionKYCExpiry()
        {
            var enableBlockingExchangeHouseUsers = await _featureManager.IsEnabledAsync(FeatureFlags.KycEnableBlockingOfExchangeHouseUsers);
            var enableKycBlockAddition = await _featureManager.IsEnabledAsync(FeatureFlags.KycEnableKycBlockAddition);

            await ProcessAdditionEventsAsync(enableKycBlockAddition, enableBlockingExchangeHouseUsers);

            return new ServiceResponse();
        }

        private List<MissingKycCardholder> RemoveDuplicatesAndMaximizeStatus(List<MissingKycCardholder> kycContainer)
        {
            var result = new Dictionary<string, MissingKycCardholder>();

            foreach (var item in kycContainer)
            {
                if (result.TryGetValue(item.CitizenId, out var existingKycMissingCardholder))
                {
                    if (item.Status > existingKycMissingCardholder.Status)
                    {
                        result[item.CitizenId] = item;
                    }
                }
                else
                {
                    result[item.CitizenId] = item;
                }
            }

            return result.Values.ToList();
        }

        private async Task ProcessNonRegisteredExpiredIdentifications()
        {
            var cardholderIdAndSerialNumberDictionary = await _unitOfWork.MissingKycCardholders.GetNonRegisteredIdentifications();
            var deletedCardholders = new List<string>();

            foreach (var item in cardholderIdAndSerialNumberDictionary)
            {
                var missingKycUserDetail = new KycMissingUserDetail { CardHolderId = item.Key, CardSerialNumber = item.Value };
                bool isSuccess = await BlockCardBasedOnKYC(missingKycUserDetail, KycStatus.Blocked);
                if (isSuccess)
                {
                    deletedCardholders.Add(item.Key);
                }
            }

            var nonRegisteredIdentifications = (await _unitOfWork.NonRegisteredIdentifications.FindAsync(x => deletedCardholders.Contains(x.CardHolderId), false)).ToList();
            var distinctCardHolderIds = nonRegisteredIdentifications.Select(s => s.CardHolderId).Distinct();
            var missingKycCardholders = new List<MissingKycCardholder>();
            var existingMissingKycCardholders = (await _unitOfWork.MissingKycCardholders.FindAsync(x => distinctCardHolderIds.Contains(x.CitizenId), false)).ToList();

            foreach (var cardholderId in distinctCardHolderIds)
            {
                var existingMissingCardholderFound = existingMissingKycCardholders.FirstOrDefault(a => a.CitizenId == cardholderId);
                if (existingMissingCardholderFound != null)
                {
                    existingMissingCardholderFound.UnblockDate = null;
                    existingMissingCardholderFound.BlockedDate = DateTime.Now;
                    existingMissingCardholderFound.Status = KycStatus.Blocked;
                    existingMissingCardholderFound.IsManuallyAdded = false;
                    existingMissingCardholderFound.Remarks = "NonRegisteredService";
                }
                else
                {
                    var missingKyc = new MissingKycCardholder
                    {
                        CitizenId = cardholderId,
                        BlockedDate = DateTime.Now,
                        Status = KycStatus.Blocked,
                        Remarks = "NonRegisteredService"
                    };

                    missingKycCardholders.Add(missingKyc);
                }
            }

            await _unitOfWork.MissingKycCardholders.AddRangeAsync(missingKycCardholders);
            nonRegisteredIdentifications.ForEach(x => x.MarkAsDeleted());
            await _unitOfWork.CommitAsync();

            _logger.LogInformation("Auto block service for non registered users is completed");
        }

        private async Task ProcessExpiryEventsAsync(bool enableBlockingExchangeHouseUsers)
        {
            foreach (var status in new[] { KycStatus.ExpiringSoon, KycStatus.Expired, KycStatus.ExpiredAndInGrace, KycStatus.Blocked })
            {
                var addMissingKycCardholdersContainer = new List<MissingKycCardholder>();
                var updateMissingKycCardholdersContainer = new List<MissingKycCardholder>();
                var addRmtKycRefinementRecords = new List<RmtKycRefinement>();
                var usersToBeUpdated = new List<User>();
                var kycExpiryEvents = new List<Tuple<string, KycExpiryEvent>>();
                var cardHolderIds = new List<string>();

                var kycMissingUsers = await _unitOfWork.MissingKycCardholders.GetExpiredKycUserData(status, enableBlockingExchangeHouseUsers);

                foreach (var item in kycMissingUsers)
                {
                    if (status == KycStatus.Blocked)
                    {
                        if (!item.CorporateId.StartsWith("4") && !item.CorporateId.StartsWith("7"))
                        {
                            try
                            {
                                var userExistWithSameAction = await _unitOfWork.RmtKycRefinement
                                    .CheckIfUserExistWithSameAction(
                                    item.UserId,
                                    item.CardHolderId,
                                    item.EmiratesId,
                                    item.EmiratesIdExpiryDate,
                                    RmtKycRefinementActions.Delete);

                                if (!userExistWithSameAction)
                                {
                                    var rmtProfileTobeDeletedOnRak = new RmtKycRefinement
                                    {
                                        CardHolderId = item.CardHolderId,
                                        EmiratesId = item.EmiratesId,
                                        UserId = item.UserId,
                                        RequiredAction = RmtKycRefinementActions.Delete.ToString(),
                                        EmiratesIdExpiryDate = item.EmiratesIdExpiryDate,
                                        UpdatedDate = DateTime.Now
                                    };

                                    addRmtKycRefinementRecords.Add(rmtProfileTobeDeletedOnRak);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"Error While inserting D flag in RmtKycRefinement: {ex.Message}");
                            }
                        }


                        bool isSuccess = await BlockCardBasedOnKYC(item, KycStatus.Blocked, deactivateRmt: false);
                        if (isSuccess)
                        {
                            if (item.MissingKycAdded)
                            {
                                updateMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                {
                                    CitizenId = item.CardHolderId,
                                    Status = status,
                                    UpdatedDate = DateTime.Now,
                                    Remarks = null,
                                    UnblockDate = null,
                                    BlockedDate = DateTime.Now,
                                    IsManuallyAdded = false
                                });

                            }
                            else
                            {
                                addMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                {
                                    CitizenId = item.CardHolderId,
                                    UpdatedDate = DateTime.Now,
                                    Remarks = null,
                                    Status = status
                                });
                            }

                            kycExpiryEvents.Add(new Tuple<string, KycExpiryEvent>(item.CardHolderId, new KycExpiryEvent()
                            {
                                NationalityCode = item.NationalityCode,
                                BranchId = item.CorporateId,
                                Status = KycExpiryEvent.GetStatusDescription(status)
                            }));
                            cardHolderIds.Add(item.CardHolderId);
                        }
                    }

                    else if (status == KycStatus.ExpiredAndInGrace)
                    {

                        if (!item.IsDeleted)
                        {
                            var sent = await _textMessageSenderService.SendKycGracePeriod(item.PhoneNumber, true);
                            if (sent != null && sent.IsSuccessful)
                            {
                                if (item.MissingKycAdded)
                                {
                                    updateMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                    {
                                        CitizenId = item.CardHolderId,
                                        Status = status,
                                        UpdatedDate = DateTime.Now,
                                        Remarks = null,
                                        IsManuallyAdded = false
                                    });

                                }
                                else
                                {
                                    addMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                    {
                                        CitizenId = item.CardHolderId,
                                        UpdatedDate = DateTime.Now,
                                        Remarks = null,
                                        Status = status
                                    });
                                }

                                kycExpiryEvents.Add(new Tuple<string, KycExpiryEvent>(item.CardHolderId, new KycExpiryEvent()
                                {
                                    NationalityCode = item.NationalityCode,
                                    BranchId = item.CorporateId,
                                    Status = KycExpiryEvent.GetStatusDescription(status)
                                }));
                            }
                        }
                        else
                        {
                            if (item.MissingKycAdded)
                            {
                                updateMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                {
                                    CitizenId = item.CardHolderId,
                                    Status = status,
                                    UpdatedDate = DateTime.Now,
                                    Remarks = null,
                                    IsManuallyAdded = false
                                });

                            }
                            else
                            {
                                addMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                {
                                    CitizenId = item.CardHolderId,
                                    Remarks = null,
                                    Status = status,
                                    UpdatedDate = DateTime.Now,
                                });
                            }

                            kycExpiryEvents.Add(new Tuple<string, KycExpiryEvent>(item.CardHolderId, new KycExpiryEvent()
                            {
                                NationalityCode = item.NationalityCode,
                                BranchId = item.CorporateId,
                                Status = KycExpiryEvent.GetStatusDescription(status)
                            }));

                        }

                    }
                    else
                    {
                        if (item.MissingKycAdded)
                        {
                            updateMissingKycCardholdersContainer.Add(new MissingKycCardholder
                            {
                                CitizenId = item.CardHolderId,
                                Status = status,
                                UpdatedDate = DateTime.Now,
                                Remarks = null,
                                IsManuallyAdded = false
                            });

                        }
                        else
                        {
                            addMissingKycCardholdersContainer.Add(new MissingKycCardholder
                            {
                                CitizenId = item.CardHolderId,
                                Status = status,
                                UpdatedDate = DateTime.Now,
                                Remarks = null
                            });
                        }

                        kycExpiryEvents.Add(new Tuple<string, KycExpiryEvent>(item.CardHolderId, new KycExpiryEvent()
                        {
                            NationalityCode = item.NationalityCode,
                            BranchId = item.CorporateId,
                            Status = KycExpiryEvent.GetStatusDescription(status)
                        }));
                    }
                }

                await PublishEventsAsync(_cleverTapServiceSettings.EventsNames.KycExpiry, kycExpiryEvents);
                if (cardHolderIds.Any())
                {
                    usersToBeUpdated = (await _unitOfWork.Users.FindAsync(f => cardHolderIds.Contains(f.CardHolderId), true)).ToList();
                    usersToBeUpdated.ForEach(f => f.MoneyTransferProfileStatus = MoneyTransferProfileStatus.Missing);
                }

                var uniqueAdd = RemoveDuplicatesAndMaximizeStatus(addMissingKycCardholdersContainer);
                var uniqueUpdate = RemoveDuplicatesAndMaximizeStatus(updateMissingKycCardholdersContainer);


                await _unitOfWork.Users.BulkUpdateAsync(usersToBeUpdated);
                await _unitOfWork.MissingKycCardholders.BulkInsertAsync(uniqueAdd);
                await _unitOfWork.MissingKycCardholders.BulkUpdateAsync(uniqueUpdate);
                await _unitOfWork.RmtKycRefinement.BulkInsertAsync(addRmtKycRefinementRecords);
                _logger.LogInformation("Auto block service for registered cardholders is completed");

            }
        }

        private async Task ProcessAdditionEventsAsync(bool enableKycBlockAddition, bool enableBlockingExchangeHouseUsers)
        {
            if (!enableKycBlockAddition) return;

            foreach (var status in new[] { KycStatus.NotSubmittedKycGraceFirstReminder, KycStatus.NotSubmittedKycGraceSecondReminder, KycStatus.NotSubmittedKycBlocked })
            {
                var addMissingKycCardholdersContainer = new List<MissingKycCardholder>();
                var updateMissingKycCardholdersContainer = new List<MissingKycCardholder>();
                var usersToBeUpdated = new List<User>();
                var kycExpiryEvents = new List<Tuple<string, KycExpiryEvent>>();
                var cardHolderIds = new List<string>();

                var kycMissingUsers = await _unitOfWork.MissingKycCardholders.GetExpiredKycUserData(status, enableBlockingExchangeHouseUsers);
                foreach (var item in kycMissingUsers)
                {

                    if (status == KycStatus.NotSubmittedKycBlocked)
                    {
                        bool isSuccess = await BlockCardBasedOnKYC(item, KycStatus.NotSubmittedKycBlocked, deactivateRmt: false);
                        if (isSuccess)
                        {
                            if (item.MissingKycAdded)
                            {
                                updateMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                {
                                    CitizenId = item.CardHolderId,
                                    Status = status,
                                    UpdatedDate = DateTime.Now,
                                    Remarks = null,
                                    UnblockDate = null,
                                    BlockedDate = DateTime.Now,
                                    IsManuallyAdded = false
                                });

                            }
                            else
                            {
                                addMissingKycCardholdersContainer.Add(new MissingKycCardholder
                                {
                                    CitizenId = item.CardHolderId,
                                    Remarks = null,
                                    Status = status,
                                    UpdatedDate = DateTime.Now,
                                });
                            }

                            kycExpiryEvents.Add(new Tuple<string, KycExpiryEvent>(item.CardHolderId, new KycExpiryEvent()
                            {
                                NationalityCode = item.NationalityCode,
                                BranchId = item.CorporateId,
                                Status = KycExpiryEvent.GetStatusDescription(status)
                            }));
                            cardHolderIds.Add(item.CardHolderId);
                        }
                    }
                    else
                    {
                        if (item.MissingKycAdded)
                        {
                            updateMissingKycCardholdersContainer.Add(new MissingKycCardholder
                            {
                                CitizenId = item.CardHolderId,
                                Status = status,
                                UpdatedDate = DateTime.Now,
                                Remarks = null,
                                IsManuallyAdded = false
                            });

                        }
                        else
                        {
                            addMissingKycCardholdersContainer.Add(new MissingKycCardholder
                            {
                                CitizenId = item.CardHolderId,
                                Status = status,
                                UpdatedDate = DateTime.Now,
                                Remarks = null
                            });
                        }

                        kycExpiryEvents.Add(new Tuple<string, KycExpiryEvent>(item.CardHolderId, new KycExpiryEvent()
                        {
                            NationalityCode = item.NationalityCode,
                            BranchId = item.CorporateId,
                            Status = KycExpiryEvent.GetStatusDescription(status)
                        }));
                    }
                }

                await PublishEventsAsync(_cleverTapServiceSettings.EventsNames.KycNotSubmitted, kycExpiryEvents);

                if (cardHolderIds.Any())
                {
                    usersToBeUpdated = (await _unitOfWork.Users.FindAsync(f => cardHolderIds.Contains(f.CardHolderId), true)).ToList();
                    usersToBeUpdated.ForEach(f => f.MoneyTransferProfileStatus = MoneyTransferProfileStatus.Missing);

                }

                var uniqueAdd = RemoveDuplicatesAndMaximizeStatus(addMissingKycCardholdersContainer);
                var uniqueUpdate = RemoveDuplicatesAndMaximizeStatus(updateMissingKycCardholdersContainer);

                await _unitOfWork.Users.BulkUpdateAsync(usersToBeUpdated);
                await _unitOfWork.MissingKycCardholders.BulkInsertAsync(uniqueAdd);
                await _unitOfWork.MissingKycCardholders.BulkUpdateAsync(uniqueUpdate);
                _logger.LogInformation("Auto block service for registered cardholders with passport is completed");

            }
        }

        private async Task PublishEventsAsync(string eventName, List<Tuple<string, KycExpiryEvent>> events)
        {
            try
            {
                // Example of handling a list of tuples. Adjust based on actual API requirements.
                await _analyticsPublisherService.PublishEventList<KycExpiryEvent>(eventName, events);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unable to send events to analytics service");
            }
        }

        private async Task<bool> BlockCardBasedOnKYC(KycMissingUserDetail kycMissingUserDetail, KycStatus kycStatus, bool deactivateRmt = false)
        {
            try
            {
                var reason = ConstantParam.AdditionKycBlockRemark;
                if (kycStatus == KycStatus.Blocked)
                {
                    reason = ConstantParam.UpdateKycBlockRemark;
                }

                var blockedKycCardholder = await _esmoWebService.BlockCard(new BlockCardRequest()
                {
                    SerialNumber = kycMissingUserDetail.CardSerialNumber,
                    Remarks = reason,
                    HardBlock = true,
                    Username = ConstantParam.SenderUsername
                });

                if (!blockedKycCardholder.IsSuccessful)
                {
                    _logger.LogWarning("KYC block :: Can't block card {cardSerialNumber}. Error: {errorMessage}", kycMissingUserDetail.CardSerialNumber, blockedKycCardholder.ErrorMessage);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "KYC block :: Can't block card {cardHolderId}", kycMissingUserDetail.CardHolderId);
                return false;
            }

            return true;
        }

        public async Task DeactivateRmt(string cardHolderId, string emiratesId, Guid userId, bool belongsToExchangeHouse)
        {
            var enableDeactivateRmt = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableDeactivateRmt);
            if (enableDeactivateRmt && !belongsToExchangeHouse)
            {
                var validateEmiratesId = ValidateEmiratesId(emiratesId);
                if (validateEmiratesId.IsSuccessful == false || validateEmiratesId.Data == false)
                {
                    return;
                }

                var enableDeactivateRmtSkip = await _featureManager.IsEnabledAsync(FeatureFlags.MoneyTransferEnableSkipDeactivateRmt);
                if (enableDeactivateRmtSkip)
                {
                    try
                    {
                        var otherEIDCardHoldersExist = await this._unitOfWork.CardHolders.Any(c => c.Id != cardHolderId && c.EmiratesId == emiratesId && c.EmployeeStatus != EmployeeStatus.Deleted.ToString());
                        if (otherEIDCardHoldersExist)
                        {
                            var otherEIDCardHolders = await this._unitOfWork.CardHolders.FindAsync(c => c.Id != cardHolderId && c.EmiratesId == emiratesId && c.EmployeeStatus != EmployeeStatus.Deleted.ToString());
                            var otherEIDCardHoldersIds = otherEIDCardHolders.Select(c => c.Id);

                            _logger.LogWarning("Profile Deletion Skip: Skipped profile deletion due to other existing cardholders: {cardholderIds}", string.Join(", ", otherEIDCardHoldersIds));

                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Profile Deletion Skip: Exception {cardHolderId}", cardHolderId, ex);
                    }
                }

                var result = await _kycService.DeactivateRmt(new DeactivateRmtDto
                {
                    CitizenId = cardHolderId,
                    EmiratesId = emiratesId
                });

                if (result.IsSuccessful)
                {
                    // Tracker Log for RMT Profile Deletion 
                    var trackerLog = new MoneyTransferProfileTracker(userId, cardHolderId, emiratesId);
                    trackerLog.ProfileDeleted();
                    await _unitOfWork.MoneyTransferProfileTracker.AddAsync(trackerLog);
                }
                else
                {
                    _logger.LogInformation($"Custom log alert to be added here: Deactivate RMT failed {cardHolderId}");
                }
            }
        }

        //public async Task<string> GenerateAuthenticationToken(string phoneNumber, Guid userId)
        //{
        //    string cacheName = GetUserPhoneNumberCacheName(phoneNumber);

        //    var cachedUserPhoneNumber = await _cacheService.GetRecordAsync<UserPhoneNumberCache>(cacheName);
        //    if (cachedUserPhoneNumber == null)
        //    {
        //        cachedUserPhoneNumber = UserPhoneNumberCache.Set(phoneNumber, userId);
        //        await _cacheService.SetRecordAsync(cacheName, cachedUserPhoneNumber, TimeSpan.FromDays(1));
        //    }
        //    string encryptedToken = AuthTokenManagerService.Encrypt($"{phoneNumber}{userId}", _generalSettings.AuthenticationTokenSecretKey);
        //    return encryptedToken;
        //}


        //public async Task<bool> ValidateAuthenticationToken(string token, string phoneNumber)
        //{
        //    var _uatPentestPhoneNumbers = _generalSettings.UATPentestPhoneNumbers.Split(";").ToList();

        //    if (_uatPentestPhoneNumbers.Count == 0)
        //        return await Validate(token, phoneNumber);
        //    else if (!_uatPentestPhoneNumbers.Contains(phoneNumber))
        //        return true;

        //    return await Validate(token, phoneNumber);

        //}

        //private async Task<bool> Validate(string token, string phoneNumber)
        //{
        //    string cacheName = GetUserPhoneNumberCacheName(phoneNumber);

        //    var cachedUserPhoneNumber = await _cacheService.GetRecordAsync<UserPhoneNumberCache>(cacheName);
        //    if (cachedUserPhoneNumber == null)
        //    {
        //        cachedUserPhoneNumber = await UpdateCachedUserPhoneNumber(phoneNumber);
        //    }

        //    string decryptedToken = AuthTokenManagerService.Decrypt(token, _generalSettings.AuthenticationTokenSecretKey);
        //    return decryptedToken.Equals($"{phoneNumber}{cachedUserPhoneNumber.Id}");
        //}

        //private async Task<UserPhoneNumberCache> UpdateCachedUserPhoneNumber(string phoneNumber)
        //{
        //    var user = await _unitOfWork.Users.FirstOrDefaultAsync(record =>
        //        record.PhoneNumber == phoneNumber &&
        //        record.ApplicationId == MobileApplicationId.C3Pay &&
        //        !record.IsDeleted);

        //    var cachedUserPhoneNumber = UserPhoneNumberCache.Set(phoneNumber, user.Id);
        //    await _cacheService.SetRecordAsync(GetUserPhoneNumberCacheName(phoneNumber), cachedUserPhoneNumber, TimeSpan.FromDays(1));

        //    return cachedUserPhoneNumber;
        //}

        public async Task<bool> ValidateDeviceToken(string phoneNumber, string passedToken)
        {
            var userCache = await _cacheService.GetRecordAsync<UserCache>(UserCache.GetKeyName(phoneNumber));
            if (userCache == null)
                return false;
            string decryptedToken = AuthTokenManagerService.Decrypt(passedToken, _generalSettings.AuthenticationTokenSecretKey);
            if (decryptedToken.Equals($"{phoneNumber}{userCache.DeviceId}") && userCache.DeviceTokenExpiryEpoch >= DateTimeOffset.Now.ToUnixTimeSeconds())
            {
                var newCache = UserCache.Set(phoneNumber, userCache.Id, userCache.DeviceId, userCache.DeviceToken, DateTimeOffset.Now.ToUnixTimeSeconds() + _generalSettings.DeviceTokenExpirySeconds);
                await _cacheService.SetRecordAsync(UserCache.GetKeyName(phoneNumber), newCache, TimeSpan.FromDays(1));
                return true;
            }
            return false;
        }


        public async Task UpdateUserCacheForDeviceBinding(string phoneNumber)
        {
            var user = await _unitOfWork.Users.GetC3payActiveUserAsync(phoneNumber);

            if (user != null)
            {
                string deviceId = Random();
                string encryptedToken = AuthTokenManagerService.Encrypt($"{phoneNumber}{deviceId}", _generalSettings.AuthenticationTokenSecretKey);
                long expiryTime = DateTimeOffset.Now.ToUnixTimeSeconds() + _generalSettings.DeviceTokenExpirySeconds;
                var updatedCache = UserCache.Set(phoneNumber, user.Id, deviceId, encryptedToken, expiryTime);
                await _cacheService.SetRecordAsync(UserCache.GetKeyName(phoneNumber), updatedCache, TimeSpan.FromDays(1));
            }
        }

        private async Task<UserCache> RefetchUserCache(string phoneNumber)
        {
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(record =>
                record.PhoneNumber == phoneNumber &&
                record.ApplicationId == MobileApplicationId.C3Pay &&
                !record.IsDeleted, a => a.Devices.Where(a => a.IsActive == true && a.IsDeleted == false && !string.IsNullOrEmpty(a.UniqueDeviceId)));

            if (user != null)
            {
                string deviceId = user.Devices.Any() ? user.Devices.FirstOrDefault().UniqueDeviceId : string.Empty;
                string encryptedToken = AuthTokenManagerService.Encrypt($"{phoneNumber}{deviceId}", _generalSettings.AuthenticationTokenSecretKey);
                long expiryTime = DateTimeOffset.Now.ToUnixTimeSeconds() + _generalSettings.DeviceTokenExpirySeconds;
                var updatedCache = UserCache.Set(phoneNumber, user.Id, deviceId, encryptedToken, expiryTime);
                await _cacheService.SetRecordAsync(UserCache.GetKeyName(phoneNumber), updatedCache, TimeSpan.FromDays(1));
                return updatedCache;
            }
            return null;
        }

        private async Task ClearCache(string phoneNumber)
        {
            UserCache userCache = null;
            await _cacheService.SetRecordAsync(UserCache.GetKeyName(phoneNumber), userCache, TimeSpan.FromDays(1));
        }

        private string Random()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()-_=+";
            int length = 50;
            StringBuilder text = new StringBuilder();
            for (int i = 0; i < length; i++)
            {
                text.Append(chars[new Random().Next(chars.Length)]);
            }
            return text.ToString();
        }

        public async Task<HardBlockType> GetUserHardBlockType(string cardHolderId)
        {
            var kycBlocked = (await _unitOfWork.MissingKycCardholders.FindAsync(u => u.CitizenId == cardHolderId
                    && (u.Status == KycStatus.NotSubmittedKycBlocked || u.Status == KycStatus.Blocked))).FirstOrDefault();

            return kycBlocked != null ? HardBlockType.KYC : HardBlockType.Admin;
        }

        public async Task<ServiceResponse> UpdateEidNumber(UpdateEidNumberDto updateEidNumberDto)
        {
            //Validations
            if (string.IsNullOrEmpty(updateEidNumberDto.CardHolderId))
            {
                return new ServiceResponse("CardHolderId cannot be empty");
            }

            if (string.IsNullOrEmpty(updateEidNumberDto.NewEidNumber) || updateEidNumberDto.NewEidNumber.Length != 15)
            {
                return new ServiceResponse("New EID number length should be 15 chars");
            }

            var userData = await GetUserByCitizenId(updateEidNumberDto.CardHolderId);
            if (!userData.IsSuccessful)
            {
                return userData;
            }
            else
            {
                var user = userData.Data;
                user.MoneyTransferProfileStatus = MoneyTransferProfileStatus.Missing;
                user.IsVerified = false;
                user.CardHolder.EmiratesId = updateEidNumberDto.NewEidNumber;

                //Reject all identifications
                var identifications = await _unitOfWork.Identifications.FindAsync(f => f.UserId == user.Id, withoutTraking: false);
                foreach (var item in identifications)
                {
                    item.VerificationStatus = IdentificationVerificationStatus.Rejected;
                }

                //Set all beneficiaries to pending and update the EID number
                var moneyTransferBeneficiaries = await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(f => f.UserId == user.Id &&
                                                                                                             f.TransferType != MoneyTransferType.DirectTransfer &&
                                                                                                             !f.IsDeleted,
                                                                                                        withoutTraking: false);
                foreach (var item in moneyTransferBeneficiaries)
                {
                    item.Status = Status.PENDING;
                    item.DocumentNumber = updateEidNumberDto.NewEidNumber;
                }

                await _unitOfWork.CommitAsync();
            }

            return new ServiceResponse();
        }


        #region International Phone Number Sign Up
        public async Task<ServiceResponse<PhoneEligibilityResult>> GetPhoneNumberEligibilityV2(string phoneNumber, bool checkIdentity)
        {
            // First we need to check if the phone number is a UAE number or an international number.
            // Phone number format: always starts with "00" followed by the country code and number.
            // Example: UAE => 00971XXXXXXXX, India => 0091XXXXXXXXXX
            PhoneEligibilityResult eligibility;

            if (phoneNumber.IsUaePhoneNumber())
            {
                // UAE phone number validation:
                // The 7th character (index 6) should be one of the allowed digits.
                // These represent valid prefixes for UAE numbers: '0', '2', '4', '5', '6', '8'
                if (!_allowedSecondPhoneNumberDigits.Contains(phoneNumber[6]))
                {
                    // Invalid UAE phone number
                    return new ServiceResponse<PhoneEligibilityResult>(PhoneEligibilityResult.InvalidPhoneNumber);
                }
            }
            else
            {
                // International phone number validation:
                // TODO: Validate based on country-specific ValidationRegex.
            }

            // At this point, the phone number format is considered valid (either UAE or international)

            // Check if a user already exists with this phone number
            Expression<Func<User, bool>> findUserFilter = x =>
                x.PhoneNumber == phoneNumber &&
                !x.IsDeleted &&
                x.ApplicationId == MobileApplicationId.C3Pay;

            var user = await _unitOfWork.Users.FirstOrDefaultAsync(findUserFilter);

            if (user != null)
            {
                eligibility = PhoneEligibilityResult.AlreadyRegistered;

                // Check feature flag to allow removing users whose cards are deleted
                var removeUserIfCardIsDeletedEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.RemoveUserIfCardIsDeleted);

                if (removeUserIfCardIsDeletedEnabled)
                {
                    var tryGetCardDetails = await _cardHolderService.GetCardHolderByCitizenId(user.CardHolderId);

                    // If the card no longer exists, remove the user and free up the phone number
                    if (!tryGetCardDetails.IsSuccessful &&
                        tryGetCardDetails.ErrorMessage == EnumUtility.GetDescriptionFromEnumValue(Enums.GetCardHolderDetails.ESGCHDS004))
                    {
                        await RemoveUserAccount(user.Id);
                        eligibility = PhoneEligibilityResult.Eligible;
                        _logger.LogInformation("The card is deleted, phone number freed up");
                    }
                }
                else
                {
                    _logger.LogWarning($"{FeatureFlags.RemoveUserIfCardIsDeleted} flag is off");
                }
            }
            else
            {
                // No user found, optionally check if identity exists
                if (checkIdentity)
                {
                    var identityExistsResult = await _identityService.UserAccountExistsAsync(phoneNumber);

                    if (!identityExistsResult.IsSuccessful)
                    {
                        return new ServiceResponse<PhoneEligibilityResult>(false, identityExistsResult.ErrorMessage);
                    }

                    eligibility = identityExistsResult.Data
                        ? PhoneEligibilityResult.AlreadyRegistered
                        : PhoneEligibilityResult.Eligible;
                }
                else
                {
                    eligibility = PhoneEligibilityResult.Eligible;
                }
            }

            return new ServiceResponse<PhoneEligibilityResult>(eligibility);

        }

        public async Task<ServiceResponse<User>> CreateUserV2(User newUser, BaseEnums.Version version, string cardNumber, string cvv)
        {
            // Check if user already exists by CardHolderId or PhoneNumber (for C3Pay) and is not deleted
            var userExists = await _unitOfWork.Users.Any(record =>
                (record.CardHolderId == newUser.CardHolderId ||
                (record.PhoneNumber == newUser.PhoneNumber && record.ApplicationId == MobileApplicationId.C3Pay)) &&
                !record.IsDeleted);

            if (userExists)
            {
                return new ServiceResponse<User>(false, ConstantParam.UserAlreadyRegistered);
            }

            // Retrieve card holder reference from PPS
            var tryGetCardHolderReference = await _esmoWebService.GetCardDetails(new GetCardDetailsRequest
            {
                CardNumber = cardNumber
            });

            if (!tryGetCardHolderReference.IsSuccessful)
            {
                return new ServiceResponse<User>(false, ConstantParam.InEligibleCard);
            }

            var cardHolderReference = tryGetCardHolderReference.Data;

            // Get the CardHolder entity from our DB using the PPS reference
            var cardHolderResult = await _cardHolderService.GetCardHolderByCitizenId(cardHolderReference.CardholderRef);
            if (!cardHolderResult.IsSuccessful)
            {
                return new ServiceResponse<User>(false, cardHolderResult.ErrorMessage);
            }

            var cardHolder = cardHolderResult.Data;

            // Feature flag check: if dormant cards are allowed
            var allowDormantCardsRegistration = await _featureManager.IsEnabledAsync(FeatureFlags.AllowDromantCardsRegistration);
            if (cardHolder.EmployeeStatus == EmployeeStatus.Dormant.ToString() && !allowDormantCardsRegistration)
            {
                _logger.LogInformation("Blocked dormant card from being registered: {cardSerialNumber}", cardHolder.CardSerialNumber);
                return new ServiceResponse<User>(false, ConstantParam.InEligibleCard);
            }

            // Ensure the corporate is allowed for exchange house users
            var allowedCorporates = await _partnerCorporateService.GetPartnerCorporates();
            if (cardHolder.BelongsToExchangeHouse)
            {
                var isCorporateAllowed = allowedCorporates.Any(x => x.CorporateId == cardHolder.CorporateId);
                if (!isCorporateAllowed)
                {
                    return new ServiceResponse<User>(false, ConstantParam.InEligibleCard);
                }
            }

            // Version V2: validate CVV logic
            if (version == BaseEnums.Version.V2)
            {
                if (string.IsNullOrEmpty(cvv))
                    return new ServiceResponse<User>(false, ConstantParam.Invalid);

                var cardSerialNumber = cardHolder.CardSerialNumber;

                // Check if card is blocked due to KYC
                var kycBlocked = (await _unitOfWork.MissingKycCardholders.FindAsync(u =>
                    u.CitizenId == cardHolder.Id &&
                    (u.Status == KycStatus.NotSubmittedKycBlocked || u.Status == KycStatus.Blocked))).FirstOrDefault();

                if (kycBlocked != null)
                {
                    var status = await _ppsService.GetCardStatus(cardSerialNumber);
                    if (status.IsSuccessful && status.Data == "Blocked")
                    {
                        var tryUnblockCard = await _ppsService.UnblockCard(cardHolder.CardSerialNumber);
                        if (!tryUnblockCard.IsSuccessful)
                        {
                            _logger.LogWarning($"Can't unblock card. Error: {tryUnblockCard.ErrorMessage}");
                            return new ServiceResponse<User>(false, CardDetailsResults.IncorrectCvc2.ToString());
                        }
                    }
                    else
                    {
                        kycBlocked = null; // Not actually blocked
                    }
                }

                // Validate CVV with PPS
                var verifyCVVResult = await _esmoWebService.VerifyCvc2(new VerifyCvc2Request
                {
                    CardSerialNumber = cardSerialNumber,
                    Cvc2 = cvv
                });

                // Re-block the card if it was temporarily unblocked
                if (kycBlocked != null)
                {
                    var blockedKycCardholder = await _ppsService.BlockCard(cardSerialNumber);
                    if (!blockedKycCardholder.IsSuccessful)
                    {
                        _logger.LogWarning($"Can't block card. Error: {blockedKycCardholder.ErrorMessage}");
                        return new ServiceResponse<User>(false, CardDetailsResults.IncorrectCvc2.ToString());
                    }
                }

                // Handle CVV verification failures
                if (!verifyCVVResult.IsSuccessful)
                {
                    Enum.TryParse(verifyCVVResult.ErrorMessage, out Enums.AtmPinErrors atmPinError);
                    switch (atmPinError)
                    {
                        case AtmPinErrors.MAX_PIN_TRIES_EXCEEDED:
                            await this._cardHolderService.BlockCard(cardSerialNumber);
                            return new ServiceResponse<User>(false, CardDetailsResults.BlockedTooManyFailedAttempts.ToString());
                        case AtmPinErrors.INVALID_CVC2:
                            return new ServiceResponse<User>(false, CardDetailsResults.IncorrectCvc2.ToString());
                        case AtmPinErrors.GENERAL_ERROR:
                        default:
                            return new ServiceResponse<User>(false, CardDetailsResults.Error.ToString());
                    }
                }
            }

            // Set CardHolderId on the new user
            newUser.CardHolderId = cardHolder.Id;

            // Set full CardHolder object if it's not already tracked
            var cardHolderExists = await _unitOfWork.CardHolders.Any(record => record.Id == cardHolder.Id);
            if (!cardHolderExists)
            {
                newUser.CardHolder = cardHolder;
            }

            // Handle UAE vs international phone number users
            if (newUser.PhoneNumber.IsUaePhoneNumber() == false)
            {
                newUser.IsInternationalPhoneNumberUser = true;
                await _unitOfWork.Users.AddAsync(newUser);
                await _unitOfWork.CommitAsync();
            }
            else
            {
                using (var transaction = _context.Database.BeginTransaction())
                {
                    await _unitOfWork.Users.AddAsync(newUser);
                    await _unitOfWork.CommitAsync();

                    await AddOutboxMessage(newUser, OutboxMessageTypeEnum.UserCreatedEvent.ToString());
                    await _unitOfWork.CommitAsync();

                    await transaction.CommitAsync();
                }

                // Link direct transfer beneficiaries using the same phone number
                newUser.PhoneNumber = newUser.PhoneNumber.Trim();
                var beneficiaries = await _unitOfWork.MoneyTransferBeneficiaries.FindAsync(
                    b => !b.IsDeleted &&
                         b.AccountNumber == newUser.PhoneNumber,
                    false, x => x.User, x => x.User.CardHolder);

                if (beneficiaries.Count > 0)
                {
                    var userPartnerCode = _partnerCorporateService.GetUserPartnerCode(cardHolder, allowedCorporates);

                    foreach (var beneficiary in beneficiaries)
                    {
                        var userBeneficiaryPartnerCode = _partnerCorporateService.GetUserPartnerCode(
                            beneficiary.User.CardHolder, allowedCorporates);

                        if (userPartnerCode.Trim().ToLower() == userBeneficiaryPartnerCode.Trim().ToLower())
                        {
                            beneficiary.LinkedUserId = newUser.Id;
                            beneficiary.Status = Status.APPROVED;
                            beneficiary.AccountNumber = null;
                        }
                        else
                        {
                            beneficiary.IsCrossTransfer = true;
                        }
                    }

                    await _unitOfWork.CommitAsync();
                }
            }

            // Send registration message to ESMO queue
            var completeUserRegistrationMessage = new CompleteUserRegistrationDto
            {
                AppStatus = true,
                CardSerialNo = cardHolder.CardSerialNumber,
                CorporateId = long.Parse(cardHolder.CorporateId),
                CitizenId = cardHolder.Id,
                Mobile = newUser.PhoneNumber,
                PpsAccountNumber = cardHolder.PpsAccountNumber,
                IsInternationalPhoneNumberUser = newUser.IsInternationalPhoneNumberUser
            };

            await _messagingQueueService.SendAsync(
                completeUserRegistrationMessage,
                _esmoServiceSettings.QueueConnectionString,
                _esmoServiceSettings.QueueName,
                null
            );

            // If eligible, trigger direct transfer claim queue
            if (newUser.ApplicationId == MobileApplicationId.C3Pay && newUser.IsInternationalPhoneNumberUser != true)
            {
                var claimPendingDirectTransfersMessages = new ClaimPendingDirectTransfersDto
                {
                    AccountNumber = newUser.PhoneNumber
                };

                await _messagingQueueService.SendAsync(
                    claimPendingDirectTransfersMessages,
                    _moneyTransferServiceSettings.ClaimPendingDirectTransfersQueueConnectionString,
                    _moneyTransferServiceSettings.ClaimPendingDirectTransfersQueueName,
                    null
                );
            }

            return new ServiceResponse<User>(newUser);

        }
        #endregion


        public async Task<ServiceResponse> SetAsLocalUat(User user)
        {
            user.IsInternationalPhoneNumberUser = false;
            await this._unitOfWork.CommitAsync();
            return new ServiceResponse();
        }


        public async Task<ServiceResponse> SetAsLocal(User user)
        {
            // If user was an international phone number user, and was changed to a local 
            user.IsInternationalPhoneNumberUser = false;
            await this._unitOfWork.CommitAsync();
            return new ServiceResponse();
        }

        #region Got Paid Events from Topic
        public async Task<ServiceResponse> ProcessSalaryGotPaidEvents(IEnumerable<SalaryGotPaidMessageDto> events)
        {

            var salaryEvents = new List<Tuple<string, SalaryProcessedEvent>>();
            var cardHoldersIds = events.Select(e => e.CitizenId);
            var users = await this._unitOfWork.Users.FindAsync(u => cardHoldersIds.Contains(u.CardHolderId) && !u.IsDeleted);

            foreach (var item in events)
            {

                var user = users.FirstOrDefault(u => u.CardHolderId == item.CitizenId);
                if (user != null)
                {
                    var salaryInfo = new SalaryProcessedEvent()
                    {
                        Amount = item.SalaryAmount,
                        ProcessedDate = item.ProcessedDate ?? DateTime.Now,
                    };

                    salaryEvents.Add(new Tuple<string, SalaryProcessedEvent>(item.CitizenId, salaryInfo));
                }
            }


            if (salaryEvents.Count == 0)
            {
                _logger.LogError("No salary got paid events to process.");
                return new ServiceResponse();
            }

            await _analyticsPublisherService.PublishSalaryProcessedEvent(salaryEvents);
            return new ServiceResponse();
        }
        #endregion
    }
}
