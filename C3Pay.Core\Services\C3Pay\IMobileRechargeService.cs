﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.DTOs.MobileRecharge;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Portal;
using C3Pay.Core.Models.Structs;
using Common.Core.Models;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Services
{
    public interface IMobileRechargeService
    {
        //API related
        Task<ServiceResponse<bool>> GetRechargeBeneficiaryDetailsEligibility(Guid userId, string countryCode, string phoneNUmber, string nickName);
        Task<ServiceResponse<IEnumerable<MobileRechargeProduct>>> GetProducts(Guid beneficiaryId, string productType, int? pageSize, int? pageNumber, MobileApplicationId applicationId, string corporateId, bool rechargeForMyself = false);
        Task<ServiceResponse<IEnumerable<MobileRechargeProduct>>> GetProductsV2(Guid beneficiaryId, int? pageNumber, MobileApplicationId applicationId, string corporateId, string cardHolderId, Guid userId, bool rechargeForMyself = false);
        Task<ServiceResponse<MobileRechargeBeneficiary>> AddMobileRechargeBeneficiary(MobileRechargeBeneficiary newBeneficiary, string userSelectedProviderCode);
        Task<ServiceResponse<List<MobileRechargeBeneficiaryDetails>>> GetMobileRechargeBeneficiaries(Guid userId, string rechargeType);
        Task<ServiceResponse> UpdateProvidersForMobileRechargeBeneficiary(Guid beneficiaryId);
        Task<ServiceResponse> DeleteMobileRechargeBeneficiary(Guid beneficiaryId, Guid? portalUserId = null,string portalEmailId=null);
        Task<ServiceResponse<IEnumerable<MobileRechargeProduct>>> GetCallingCardProducts(string callingCardOperator, MobileApplicationId applicationId, string corporateId);
        Task<ServiceResponse<ProductEstimatePriceDingModel>> GetProductEstimateRate(string productCode, string corporateId, MobileApplicationId applicationId);
        Task<ServiceResponse<CallingCardResponseDingModel>> SendTransfer(MobileRechargeTransaction mobileRecharge, MobileApplicationId mobileApplicationId, string phoneNumber = null, string corporateId = null);
        Task<ServiceResponse<IEnumerable<MobileRechargeDetails>>> GetUserMobileRechargeTransactions(Guid userId, string rechargeType, int? pageSize, int? pageNumber);
        Task<ServiceResponse<MobileRechargeTransaction>> GetMobileRechargeTransactionReceipt(Guid transactionId);
        Task<ServiceResponse<MobileRechargeTransaction>> GetMobileRechargeTransactionReceiptReadOnly(Guid TransactionId);
        Task<ServiceResponse> UpdateStatusForPendingTransactions(bool isRatesExperimentEnabled);
        Task<ServiceResponse<Tuple<IList<MobileRechargeBeneficiary>, int>>> GetAllMobileRechargeBeneficiaryReadOnly(SearchMobileRechargeBeneficiary searchRechargeParameters);
        Task<ServiceResponse<Tuple<IList<MobileRechargeBeneficiary>, int>>> SearchUserRechargeBeneficiariesReadOnly(Guid Id, SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse<Tuple<IList<MobileRechargeTransaction>, int>>> SearchUserRechargeBeneficiariesTransactionReadOnly(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<ServiceResponse<Tuple<List<MobileRechargeTransactionStruct>, int>>> SearchRechargeBeneficiariesTransactionReadOnly(SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<IList<Guid>> GetUserIdsByBeneficiary(SearchBeneficiaryParameters searchBeneficiaryParameters);
        Task<string> GetCountryCode(string accountNumber);

        Task<bool> SynchronizeDingCountryWithDB();
        Task<bool> SynchronizeDingRegionWithDB();
        Task<bool> SynchronizeDingProviderWithDB();
        Task<bool> SynchronizeDingProviderStatusWithDB();
        Task<bool> SynchronizeDingProductsWithDB();
        Task<bool> SynchronizeDingProductsDescriptionWithDB();
        Task<ServiceResponse<CallingCardResponseDingModel>> RepeatTransfer(Guid transactionId, MobileApplicationId c3Pay, string phoneNumber = null);
        Task<ServiceResponse<IEnumerable<MobileRechargeDetails>>> GetRecentMobileRechargeTransactions(Guid userId, MobileRechargeType mobileRechargeType, int? count);
        Task<ServiceResponse<bool>> CheckForBeneficiaryModification(Guid userId, Guid beneficiaryId);
        Task<ServiceResponse<MobileRechargeTransaction>> GetSummaryDetails(User user, string productCode,
                MobileApplicationId applicationId, MobileRechargeTransaction mobileRechargeTransaction);
        Task<ServiceResponse<CallingCardResponseDingModel>> SendTransferV2(User user, MobileRechargeTransaction mobileRechargeTransaction,
           MobileApplicationId mobileApplicationId, string phoneNumber = null, bool isAutoRenewalEnabled = false, bool isAutoRenewalJobExecution = false, bool hasDiscountOnRenewals = false);
        Task<ServiceResponse<ValidateSendTransferResponseDto>> ValidateSendTransferAsync(MobileRechargeTransaction request, MobileApplicationId mobileApplicationId);
        Task<ServiceResponse> UpdateOperator(Guid request);
        Task<List<MobileRechargeProduct>> GetLocalCallingCardProductsAsync(CancellationToken cancellationToken);

        Task<ServiceResponse<string>> CrossCheckBeneficiaryForFailedTransaction(Guid transactionId, Guid beneficiaryId,
                                        string beneficiaryCountryCode, string externalErrorMessage);
        Task<ServiceResponse<bool>> CheckForMobileRechargeDiscount(Guid userId);
        Task<ServiceResponse<MobileRechargeTransaction>> GetSummary(User user, string productCode, MobileApplicationId applicationId, MobileRechargeTransaction mobileRechargeTransaction);
        Task<ServiceResponse<bool>> UpdateRenewal(Guid userId, string productCode, Guid beneficiaryId, string cardHolderId, MobileRecharge_AutoRenewalInactiveCode? deactivationCode, bool IsActive = true, bool hasClaimedTargetedDiscount = false);
        Task<ServiceResponse<List<MobileRechargeRenewal>>> GetAllActivateRenewals (User user);
        Task<ServiceResponse<bool>> ProcessScheduledRenewals(bool isTargetedRenewalDiscountEnabled);
        Task ProcessTargetedDiscount(string citizenId, string phoneNumber);
        Task<ServiceResponse<TargetedDiscountConfigDto>> GetTargetedDiscountConfig(User user);

        Task<ServiceResponse> ReceiveDeferredRechargeResponseFromDing (string dingResponse, string correlationId, CancellationToken cancellationToken);
        Task<ServiceResponse<bool>> ProcessLowBalanceScheduledRenewals();


        Task<ServiceResponse<ValidateSendTransferResponseDto>> ValidateSendTransferAsyncV2(MobileRechargeTransaction request, MobileApplicationId mobileApplicationId);
    }
}
