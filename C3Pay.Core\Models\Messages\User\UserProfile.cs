﻿using System;
using System.Collections.Generic;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models
{
    public class UserProfile
    {
        public Guid Id { get; set; }
        public string CitizenId { get; set; }
        public string PhoneNumber { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Nationality { get; set; }
        public string CorporateName { get; set; }
        public string CorporateId { get; set; }
        public string EmployeeId { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string PlaceOfBirth { get; set; }
        public int? BirthProvinceId { get; set; }
        public int? BirthDistrictId { get; set; }
        public DateTime? RegistrationDate { get; set; }
        public string Email { get; set; }
        public string DeviceToken { get; set; }
        public string RMTProfileStatus { get; set; }
        public bool HasApprovedEmiratesIdUpdates { get; set; }
        public bool HasPendingEmiratesIdUpdates { get; set; }
        public string EmiratesIdStatus { get; set; }
        public bool HasMoneyTransferTransactions { get; set; }
        public bool HasMoneyTransferBeneficiary { get; set; }
        public bool HasLocalMobileRechargeTransactions { get; set; }
        public bool HasInternationalMobileRechargeTransactions { get; set; }
        public bool RequiresPasswordReset { get; set; }
        public string EmiratesId { get; set; }
        public DateTime? EmiratesIdExpiryDate { get; set; }
        public bool IsVerified { get; set; }
        public string CardSerialNumber { get; set; }
        public CardPlasticType CardPlasticType { get; set; }
        public bool ATMPinPopupEnabled { get; set; }
        public DateTime? ATMPinBlockEndDate { get; set; }
        public bool IsBlocked { get; set; }
        public bool AutoplayMoneyTransferVideo { get; set; }
        public string MoneyTransferVideoURL { get; set; }
        public string MoneyTransferVideoThumbnailURL { get; set; }
        public string ReferralProgramVideoURL { get; set; }
        public string ReferralProgramVideoThumbnailURL { get; set; }
        public string BillPaymentVideoURL { get; set; }
        public string BillPaymentVideoThumbnailURL { get; set; }
        public string ReferralCode { get; set; }
        public bool RewardedForReferral { get; set; }
        public int MoneyTransferReferralCount { get; set; }
        public bool HasApprovedPassportUpdates { get; set; }
        public bool HasPendingPassportUpdates { get; set; }
        public string PassportStatus { get; set; }
        public string PpsAccountNumber { get; set; }
        public string LabourCardId { get; set; }
        public bool? IsFreeZoneEmployee { get; set; }
        public string DeviceId { get; set; }
        public decimal? MoneyTransferFees { get; set; }
        public string MoneyTransferMoneyBackExperimentGroupCode { get; set; }
        public List<ExperimentUsers> ExperimentUsers { get; set; }
        public bool BelongsToExchangeHouse { get; set; }
        public string Role { get; set; }
        public string PartnerLogoUrl { get; set; }
        public string EhRatesAreBetterVideoURL { get; set; }
        public string EhRatesAreBetterThumbnailURL { get; set; }
        public List<SocialProofing> SocialProofing { get; set; }
        public string LoginVideoUrl { get; set; }
        public int MoneyTransferTransactionsCount { get; set; }
        public int MTInternationalBeneficiaryCount { get; set; }
        public List<MoneyTransferSuspiciousBeneficiaryDto> SuspiciousBeneficiaries { get; set; }
        public DateTime? RmtProfileSentDateTime { get; set; }
        public DateTime? FreeTransferExpiryDate { get; set; }
        public DateTime? MtGoldIncentiveExpiryDate { get; set; }
        public bool IsFirstSalaryProcessed { get; set; }
        public bool HasEmiratesIdIdentification { get; set; }

        // Temporarily added this flag for SMS+ rollout plan to support Mobile APP
        public bool IsSmsPlusEnabled { get; set; }

        public bool EmailConsentGiven { get; set; }

        public bool? IsInternationalPhoneNumberUser { get; set; }

        public bool IsInAppAuthUser { get; set; }

    }
}
