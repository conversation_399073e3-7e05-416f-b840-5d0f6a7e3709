﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.Configurations
{
    public class CardHolderConfiguration : IEntityTypeConfiguration<CardHolder>
    {
        public void Configure(EntityTypeBuilder<CardHolder> builder)
        {
            builder.ToTable("CardHolders");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.Id)
                .HasMaxLength(15);

            builder.Property(c => c.EmployeeId)
                .HasMaxLength(50);

            builder.Property(c => c.C3RegistrationId)
                .HasMaxLength(50);

            builder.Property(c => c.CardNumber)
                .HasMaxLength(16);

            builder.Property(c => c.CardSerialNumber)
                .HasMaxLength(10);

            builder.Property(c => c.EmiratesId)
                .HasMaxLength(15);

            builder.Property(c => c.City)
                .HasMaxLength(100);

            builder.Property(c => c.CorporateName)
                .HasMaxLength(200);

            builder.Property(c => c.CorporateId)
                .HasMaxLength(10);

            builder.Property(c => c.EmployeeStatus)
                .HasMaxLength(10);

            builder.Property(c => c.FirstName)
                .HasMaxLength(100);

            builder.Property(c => c.LastName)
                .HasMaxLength(100);

            builder.Property(c => c.Gender)
                .HasMaxLength(6);

            builder.Property(c => c.Nationality)
                .HasMaxLength(3);

            builder.Property(c => c.PassportNumber)
                .HasMaxLength(50);

            builder.Property(c => c.ZipCode)
                .HasMaxLength(20);

            builder.Property(c => c.LabourCardId)
                .HasMaxLength(50);

            builder.Property(c => c.AddressLine)
              .HasMaxLength(250);

            builder.Property(c => c.IsFreeZoneEmployee)
                .HasDefaultValue(false);

            builder.Property(c => c.HasSurchargeFee)
                .HasDefaultValue(false);

            builder
                .HasMany(c => c.Users)
                .WithOne(u => u.CardHolder)
                .IsRequired()
                .HasForeignKey(u => u.CardHolderId);

            builder
                .HasMany(c => c.ExperimentUsers)
                .WithOne(u => u.CardHolder)
                .IsRequired()
                .HasForeignKey(u => u.CardHolderId)
                .OnDelete(DeleteBehavior.Restrict);

            builder
               .HasOne(c => c.MissingKycCardholder)
               .WithOne(p => p.CardHolder)
               .HasForeignKey<MissingKycCardholder>(c => c.CitizenId);

            builder.Property(c => c.PpsAccountNumber)
                .HasMaxLength(50)
                .IsRequired(false);

            builder.HasOne(c => c.Province)
                .WithMany()
                .HasForeignKey(c => c.BirthProvinceId);

            builder.HasOne(c => c.District)
                .WithMany()
                .HasForeignKey(c => c.BirthDistrictId);

            builder.HasIndex(x => x.FirstName);
            builder.HasIndex(x => x.LastName);
            builder.HasIndex(x => x.CardSerialNumber);
            builder.HasIndex(x => x.CorporateId);
            builder.HasIndex(x => x.EmiratesId);
        }
    }
}
