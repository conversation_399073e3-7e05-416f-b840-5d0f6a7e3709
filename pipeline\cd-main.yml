﻿name: $(Date:yyyyMMdd)$(Rev:.r)

trigger: none

pool:
  vmimage: windows-latest

resources:
  repositories:
    - repository: devopsTemplates
      type: git
      endpoint: edenered-hq
      name: Architecture/devops-templates
    - repository: automationRegressionArtifacts
      type: git
      name: C3Pay.Backend/C3Pay.Backend
      ref: QA_Automation 
      
  pipelines:
    - pipeline: binaries
      source: "CI - Binaries"
      trigger:
        enabled: true
        branches:
          include:
            - master
            - Release
            - develop

    - pipeline: infra
      source: "CI - Infra"
      trigger:
        enabled: true
        branches:
          include:
            - master
            - Release
            - develop      

variables:
  global.location: "uaenorth"
  global.secondaryLocation: "uaenorth"
  global.templateLocation: "https://ehqtemplates.blob.core.windows.net/templates-v2/"
  global.infraFile: "$(Pipeline.Workspace)/infra/infra/master.json"
  global.webAppBinariesFile: "$(Pipeline.Workspace)/binaries/webapi/api.zip"
  global.portalWebAppBinariesFile: "$(Pipeline.Workspace)/binaries/portalwebapi/portalApi.zip"
  global.webJobBinariesFile: "$(Pipeline.Workspace)/binaries/webjob"
  global.SqlFile: "$(Pipeline.Workspace)/binaries/sql/C3PayContext.sql"
  global.IdentitySqlFile: "$(Pipeline.Workspace)/binaries/identitySql/C3PayIdentityDbContext.sql"
  global.RakBanksMaxRecords: 100
  global.MoneyTransferBeneficiaryCount: 20
  global.RakMaxTransactionTriesCount: 1
  global.RakMessageProcessInDelay: 60
  global.RakTransactionUpdateDelay: 1
  global.RakMoneyTransferBeneficiaryDelayInMins: 60
  global.RakLoyaltyImplementDate: "2020-11-12 00:00:00"
  global.RakLoyaltyLimitCount: 5
  global.RakLoyaltyLimitAmount: 501
  global.MobileRechargeSynchronizeWithDingSchedule: "04:00:00"
  global.MobileRechargeUpdateStatusSchedule: "04:00:00"
  global.MobileRechargeNickNameLength: 20
  global.MobileRechargeCallingCardAccountNumberLive: "**********"
  global.FirstBlackV1PlasticCardId: "**********"
  global.FirstBlackV2PlasticCardId: "**********"
  global.ReferralProgramMoneyTransferCountThreshold: 3
  global.ReferralProgramMoneyTransferAmountThreshold: 501
  global.ReferralProgramMoneyTransferRewardAmount: 100
  global.ReferralProgramMoneyTransferReferralProgramStartDate: "2021-10-06 00:00:00"
  global.ReversePendingDirectMoneyTransfersSchedule: "00:10:00"
  global.ReverseFailedDirectMoneyTransfersSchedule: "01:00:00"
  global.MobileRechargeC3FeeMode: "2"
  global.MobileRechargeMySalaryFeeMode: "2"
  global.MobileRechargeFeeAmount: "0.5"
  global.RAKSFTPBranchesDirectory: "//Branches//New//"
  global.RAKSFTPBranchesArchiveDirectory: "//Branches//Archive//"
  global.SlotDeploymentEnabled: "true"
  global.SlotDeploymentInstanceName: "staging"

stages:
  - stage: UAT_EAE
    dependsOn: []
    condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/release'))
    variables:
      - name: azureSubscription
        value: "AzureDevops-eae-c3pay-a" #'AzureDevops-eae-c3pay-a-local'
      - name: azureSubscriptionEQ
        value: "AzureDevops-eae-c3pay-a"
      - name: stage.environment
        value: "UAT"
      - name: stage.entityCode
        value: "eae"
      - name: stage.rgName
        value: "c3pay-uat-rg"
      - name: stage.appName
        value: "c3pay"
      - name: stage.dbName
        value: "C3Pay"

      - name: stage.farmSkuName
        value: "S1"
      - name: stage.webJobFarmSkuName
        value: "S1"
      - name: farmSkuCapacity
        value: 1
      - name: farmSkuCapacityMin
        value: 1
      - name: farmSkuCapacityMax
        value: 2      

      - name: stage.dbSkuName
        value: "S0"
      - name: stage.dbWeeklyLtr
        value: "1"
      - name: stage.dbMonthlyLtr
        value: "1"
      - name: stage.identityDbSkuName
        value: "S0"
      - name: stage.serviceBusSkuName
        value: "Standard"
      - name: rakCertificateName
        value: file.rak.pfx
      - name: rakPrivateKeyName
        value: rak-staging.key
      - name: rakPublicKeyName
        value: RAKBankPubKey2025_UAT.pub
      - group: "C3Pay-uat"
      - name: stage.RAKSFTPInputRootDirectory
        value: "//IN//"
      - name: stage.RAKSFTPOutputRootDirectory
        value: "//OUT//"
      - name: stage.RAKSFTPTransactionStatusDirectory
        value: "RMT//C3TxnStatus//"
      - name: stage.RAKSFTPTransactionBlobContainerName
        value: "raktransaction"
      - name: stage.RAKSFTPProfileStatusDirectory
        value: ""
      - name: stage.MobileRechargeTransactionEnvironment
        value: "UAT"
      - name: stage.MobileRechargeNonVerifiedLimit
        value: 100000
      - name: stage.MobileRechargeVerifiedLimit
        value: 100000
      - name: stage.MoneyTransferMonthlyAmountLimit
        value: 1000000
      - name: stage.MoneyTransferMonthlyCountLimit
        value: 10000
      - name: stage.ApiManagementName
        value: "eae-c3pay-apigw-a"
      - name: stage.ExistingAppServiceName
        value: "eae-test.c3pay-asp-a"
      - name: stage.ExistingWebjobServiceName
        value: "c3paysingleton-asp-a"

    jobs:
      - template: cd-jobs.yml
        parameters:
          azureSubscription: ${{ variables.azureSubscription }}
          azureSubscriptionEQ: ${{ variables.azureSubscriptionEQ }}
          targetenvironment: "UAT EAE"
          templateLocation: $(global.templateLocation)
          environment: $(stage.environment)
          entityCode: $(stage.entityCode)
          appName: $(stage.appName)
          existingAppServiceName: $(stage.ExistingAppServiceName)
          existingWebjobServiceName: $(stage.ExistingWebjobServiceName)
          location: $(global.location)
          secondaryLocation: $(global.secondaryLocation)
          rgName: $(stage.rgName)
          dbName: $(stage.dbName)
          farmSkuName: $(stage.farmSkuName)
          webJobFarmSkuName: $(stage.webJobFarmSkuName)
          farmSkuCapacity: ${{ variables.farmSkuCapacity }}
          farmSkuCapacityMin: ${{ variables.farmSkuCapacityMin }}
          farmSkuCapacityMax: ${{ variables.farmSkuCapacityMax }}
          dbSkuName: $(stage.dbSkuName)
          dbWeeklyLtr: $(stage.dbWeeklyLtr)
          dbMonthlyLtr: $(stage.dbMonthlyLtr)
          identityDbSkuName: $(stage.identityDbSkuName)
          serviceBusSkuName: $(stage.serviceBusSkuName)
          isSlotDeploymentEnabled: $(global.SlotDeploymentEnabled)
          slotDeploymentInstanceName: $(global.SlotDeploymentInstanceName)
          infraFile: $(global.infraFile)
          webAppBinariesFile: $(global.webAppBinariesFile)
          webJobBinariesFile: $(global.webJobBinariesFile)
          portalWebAppBinariesFile: $(global.portalWebAppBinariesFile)
          sqlFile: $(global.sqlFile)
          identitySqlFile: $(global.identitySqlFile)
          sqlsrvAdministratorLogin: $(sqlsrvAdministratorLogin)
          sqlsrvAdministratorPassword: $(sqlsrvAdministratorPassword)
          DevIP: $(DevIP)
          rakCertificateName: ${{ variables.rakCertificateName }}
          rakCertificatePassword: $(pfxpassword)
          rakPrivateKeyName: ${{ variables.rakPrivateKeyName }}
          rakPublicKeyName: ${{ variables.rakPublicKeyName }}
          SettingEDCAuthority: $(EDConnectAuthority)
          SettingEDCApiName: $(EDConnectApiName)
          SettingEDCApiSecret: $(EDConnectApiSecret)
          SettingPortalEDCAuthority: $(PortalEDConnectAuthority)
          SettingPortalEDCApiName: $(PortalEDConnectApiName)
          SettingPortalEDCApiSecret: $(PortalEDConnectApiSecret)
          SettingAADAuthority: $(AADAuthority)
          SettingAADAudience: $(AADAudience)
          SettingAADAllowedClientIds: $(AADAllowedClientIds)
          SettingKeyName: $(KeyName)
          SettingSignzyURL: $(SignzyURL)
          SettingSignzyFileExchangeAddress: $(SignzyFileExchangeAddress)
          SettingSignzyId: $(SignzyId)
          SettingSignzyUserId: $(SignzyUserId) 
          SettingSignzyImageQualityTimeout: $(SignzyImageQualityTimeout)
          SettingSignzyFaceMatchTimeout: $(SignzyFaceMatchTimeout)
          SettingSignzyReadDocumentTimeout: $(SignzyReadDocumentTimeout)
          SettingAzureOCREndpoint: $(AzureOCREndpoint)
          SettingAzureOCRKey: $(AzureOCRKey)
          SettingAzureOCRWaitTimeInMilliSeconds: $(AzureOCRWaitTimeInMilliSeconds)
          SettingAzureFaceEndpoint: $(AzureFaceEndpoint)
          SettingAzureFaceKey: $(AzureFaceKey)
          SettingAzureIdentificationServiceUseAzureOCR: $(AzureIdentificationServiceUseAzureOCR)
          SettingAzureIdentificationServiceUseAzureFace: $(AzureIdentificationServiceUseAzureFace)
          SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck: $(AzureIdentificationServiceMaxDifferenceInDigitsForRecheck)
          SettingKycExpiryCheckStartDate: $(KycExpiryCheckStartDate)
          SettingKycExpiryValidateEmiratesIdExpiryScheduler: $(KycExpiryValidateEmiratesIdExpiryScheduler)
          SettingKycExpiryValidateAdditionKYCScheduler: $(KycExpiryValidateAdditionKYCScheduler)
          SettingTransactionsB2CServiceAuthority: $(TransactionsB2CServiceAuthority) 
          SettingTransactionsB2CServiceClientId: $(TransactionsB2CServiceClientId) 
          SettingTransactionsB2CServiceScope: $(TransactionsB2CServiceScope) 
          SettingTransactionsB2CServiceClientSecret: $(TransactionsB2CServiceClientSecret) 
          SettingTransactionsB2CServiceBaseAddress: $(TransactionsB2CServiceBaseAddress) 
          SettingTransactionsB2CServiceGrantType: $(TransactionsB2CServiceGrantType) 
          SettingTransactionsB2CServiceAPIVersion: $(TransactionsB2CServiceAPIVersion)
          SettingTransactionsB2CServiceTimeout: $(TransactionsB2CServiceTimeout)
          SettingRakBaseURL: $(RakBaseURL)
          SettingRakClientId: $(RakClientId)
          SettingRakClientSecret: $(RakClientSecret)
          SettingRakProcessBankTransactionsReportsSchedule: $(RakProcessBankTransactionsReportsSchedule)
          SettingRakUpdatePendingBankTransactionsSchedule: $(RakUpdatePendingBankTransactionsSchedule)
          SettingRakReverseFailedBankTransactionsSchedule: $(RakReverseFailedBankTransactionsSchedule)
          SettingUploadMissingProfilesImagesSchedule: $(UploadMissingProfilesImagesSchedule)
          SettingMoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule: $(MoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule)
          SettingRakProcessRakStatementSchedule: $(RakProcessRakStatementSchedule)
          SettingRakProcessRmtSubmissionSchedule: $(RakProcessRmtSubmissionSchedule)
          SettingRakProcessRmtAcknowledgementSchedule: $(RakProcessRmtAcknowledgementSchedule)
          SettingExchangeHouseUpdateStatusSchedule: $(ExchangeHouseUpdateStatusSchedule)
          SettingRakReadRMTProfileResponsesSchedule: $(RakReadRMTProfileResponsesSchedule)
          SettingRakSftpRMTProfileResponsesDirectory: $(RakSftpRMTProfileResponsesDirectory)
          SettingRakSftpMissingRakFileAlertPhoneNumbers: $(RakSftpMissingRakFileAlertPhoneNumbers)
          SettingRakBanksMaxRecords: $(global.RakBanksMaxRecords)
          SettingRakMaxTransactionTriesCount: $(global.RakMaxTransactionTriesCount)
          SettingRakMessageProcessInDelay: $(global.RakMessageProcessInDelay)
          SettingRakTransactionUpdateDelay: $(global.RakTransactionUpdateDelay)
          SettingRakMoneyTransferBeneficiaryDelayInMins: $(global.RakMoneyTransferBeneficiaryDelayInMins)
          SettingRakRmtProfilePositiveStatus: $(RakRmtProfilePositiveStatus)
          SettingRakLoyaltyImplementDate: $(global.RakLoyaltyImplementDate)
          SettingRakLoyaltyLimitCount: $(global.RakLoyaltyLimitCount)
          SettingRakLoyaltyLimitAmount: $(global.RakLoyaltyLimitAmount)
          SettingRakURLPath: $(RakURLPath)
          SettingRakMoneyTransferBeneficiaryCount: $(global.MoneyTransferBeneficiaryCount)
          SettingRakEnableRakTokenCache: $(RakEnableRakTokenCache)
          SettingRakSftpEndPoint: $(RakSftpEndPoint)
          SettingRakSftpPort: $(RakSftpPort)
          SettingRakSftpUsername: $(RakSftpUsername)
          SettingRakSftpPassword: $(RakSftpPassword)
          SettingRAKSFTPInputRootDirectory: $(stage.RAKSFTPInputRootDirectory)
          SettingRAKSFTPOutputRootDirectory: $(stage.RAKSFTPOutputRootDirectory)
          SettingRAKSFTPTransactionStatusDirectory: $(stage.RAKSFTPTransactionStatusDirectory)
          SettingRAKSFTPTransactionBlobContainerName: $(stage.RAKSFTPTransactionBlobContainerName)
          SettingRAKSFTPProfileStatusDirectory: $(stage.RAKSFTPProfileStatusDirectory)
          SettingRAKSFTPBranchesDirectory: $(global.RAKSFTPBranchesDirectory)
          SettingRAKSFTPBranchesArchiveDirectory: $(global.RAKSFTPBranchesArchiveDirectory)
          SettingRakSftpReportDirectory: $(RakSftpReportDirectory)
          SettingRakSftpRmtAcknowledgementDirectory: $(RakSftpRmtAcknowledgementDirectory)
          SettingRakSftpRmtTempSubmissionDirectory: $(RakSftpRmtTempSubmissionDirectory)
          SettingRakRefreshRatesSchedule: $(RakRefreshRatesSchedule)
          SettingExchangeHouseRefreshRatesSchedule: $(ExchangeHouseRefreshRatesSchedule)
          SettingRakRefreshRatesEmiratesId: $(RakRefreshRatesEmiratesId)
          SettingPPSWebAuthBaseURL: $(PPSWebAuthBaseURL)
          SettingPPSWebAuthClientId: $(PPSWebAuthClientId)
          SettingPPSWebAuthClientSecretkey: $(PPSWebAuthClientSecretkey)
          SettingPPSEndpointAddress: $(PPSEndpointAddress)
          SettingPPSUsername: $(PPSUsername)
          SettingPPSPassword: $(PPSPassword)
          SettingPPSSponsorCode: $(PPSSponsorCode)
          SettingPPSCustomerCode: $(PPSCustomerCode)
          SettingPPSSharedSecret: $(PPSSharedSecret)
          SettingPPSTimeout: $(PPSTimeout)
          SettingKYCBaseAddress: $(KYCBaseAddress)
          SettingKYCUsername: $(KYCUsername)
          SettingKYCPassword: $(KYCPassword)
          SettingKYCUniqueRef: $(KYCUniqueRef)
          SettingKYCSponsorCode: $(KYCSponsorCode)
          SettingKYCSharedSecret: $(KYCSharedSecret)
          SettingEtisalatSMSUsername: $(EtisalatSMSUsername)
          SettingEtisalatSMSPassword: $(EtisalatSMSPassword)
          SettingEtisalatSMSBaseAddress: $(EtisalatSMSBaseAddress)
          SettingEtisalatSMSTimeout: $(EtisalatSMSTimeout)
          SettingEtisalatSMSRetryCount: $(EtisalatSMSRetryCount)
          SettingInfobipSMSUsername: $(InfobipSMSUsername)
          SettingInfobipSMSPassword: $(InfobipSMSPassword)
          SettingInfobipSMSBaseAddress: $(InfobipSMSBaseAddress)
          SettingInfobipSMSAuthKey: $(InfobipSMSAuthKey)
          SettingInfobipSMSAuthKeyBaseUrl: $(InfobipSMSAuthKeyBaseUrl)
          SettingInfobipSMSSmsMode: $(InfobipSMSSmsMode)
          SettingInfobipSMSTimeout: $(InfobipSMSTimeout)
          SettingInfobipSMSRetryCount: $(InfobipSMSRetryCount)
          SettingDingBaseURL: $(DingBaseURL)
          SettingDingClientApiKey: $(DingClientApiKey)
          SettingSecondaryDingClientApiKey: $(SecondaryDingClientApiKey)
          SettingMobileRechargeSynchronizeWithDingSchedule: $(global.MobileRechargeSynchronizeWithDingSchedule)
          SettingMobileRechargeUpdateStatusSchedule: $(global.MobileRechargeUpdateStatusSchedule)
          SettingMobileRechargeNickNameLength: $(global.MobileRechargeNickNameLength)
          SettingMobileRechargeC3FeeMode: $(global.MobileRechargeC3FeeMode)
          SettingMobileRechargeMySalaryFeeMode: $(global.MobileRechargeMySalaryFeeMode)
          SettingMobileRechargeSelectedCorporatesWithFee: $(MobileRechargeServiceSelectedCorporatesWithFee)
          SettingMobileRechargeFeeAmount: $(global.MobileRechargeFeeAmount)
          SettingMobileRechargeCallingCardAccountNumberLive: $(global.MobileRechargeCallingCardAccountNumberLive)
          SettingMobileRechargeTransactionEnvironment: $(stage.MobileRechargeTransactionEnvironment)
          SettingMobileRechargeNonVerifiedLimit: $(stage.MobileRechargeNonVerifiedLimit)
          SettingMobileRechargeVerifiedLimit: $(stage.MobileRechargeVerifiedLimit)
          SettingMobileRechargeCustomCallingCardName: $(MobileRechargeCustomCallingCardName)
          SettingMobileRechargeCustomCallingCardCode: $(MobileRechargeCustomCallingCardCode)
          SettingMobileRechargeCustomCallingCardLogoUrl: $(MobileRechargeCustomCallingCardLogoUrl)
          SettingMobileRechargeCustomCallingCardValidationRegex: $(MobileRechargeCustomCallingCardValidationRegex)
          SettingMobileRechargeDynamicPackageSeperator: $(MobileRechargeDynamicPackageSeperator)
          SettingMobileRechargeServiceServiceBusTopicName: $(MobileRechargeServiceServiceBusTopicName)
          SettingMobileRechargeServiceServiceBusSubscriptionName: $(MobileRechargeServiceServiceBusSubscriptionName)
          SettingSurchargeServiceServiceBusTopicName: $(SurchargeServiceServiceBusTopicName)
          SettingMobileRechargeServiceRenewalSchedule: $(MobileRechargeServiceRenewalSchedule)
          SettingMobileRechargeServiceLowBalanceRenewalSchedule: $(MobileRechargeServiceLowBalanceRenewalSchedule)
          SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays: $(MobileRechargeServiceLowBalanceRetryMaxThresholdInDays)
          SettingVpnMembershipRenewalSchedule: $(VpnMembershipRenewalSchedule)
          SettingRenewalCardUpdateServiceBusTopicName: $(RenewalCardUpdateServiceBusTopicName)
          SettingRenewalCardUpdateServiceBusSubscriptionName: $(RenewalCardUpdateServiceBusSubscriptionName)
          SettingMoneyTransferMultimediaURL: $(MoneyTransferMultimediaURL)
          SettingMoneyTransferRefreshBanksAndBranchesSchedule: $(RefreshBanksAndBranchesSchedule)
          SettingMoneyTransferReversalStartDate: $(MoneyTransferReversalStartDate)
          SettingMoneyTransferReversalMode: $(MoneyTransferReversalMode)
          SettingMoneyTransferMonthlyAmountLimit: $(stage.MoneyTransferMonthlyAmountLimit)
          SettingMoneyTransferFreeTransferExpiryScheduler: $(MoneyTransferFreeTransferExpiryScheduler)
          SettingMoneyTransferRetryBeneficiarySchedule: $(MoneyTransferRetryBeneficiarySchedule)
          SettingMoneyTransferMaxBeneficiaryRetryLimit: $(MoneyTransferMaxBeneficiaryRetryLimit)
          SettingMoneyTransferRetryBeneficiaryDurationInMin: $(MoneyTransferRetryBeneficiaryDurationInMin)
          SettingMoneyTransferMonthlyCountLimit: $(stage.MoneyTransferMonthlyCountLimit)
          SettingMoneyTransferEnableRakMock: $(MoneyTransferEnableRakMock)
          SettingMoneyTransferEnableRakNegativeScenarioMock: $(MoneyTransferEnableRakNegativeScenarioMock)
          SettingMoneyTransferComparisonReceiveAmount: $(MoneyTransferComparisonReceiveAmount)
          SettingMoneyTransferComparisonEHTransferFee: $(MoneyTransferComparisonEHTransferFee)
          SettingMoneyTransferComparisonEHRateIncrement: $(MoneyTransferComparisonEHRateIncrement)
          SettingMoneyTransferGWNationalities: $(MoneyTransferGWNationalities)
          SettingMoneyTransferGWLanguages: $(MoneyTransferGWLanguages)
          SettingMoneyTransferGWStartDate: $(MoneyTransferGWStartDate)
          SettingMoneyTransferGeneralDefaultAmount: $(MoneyTransferGeneralDefaultAmount)
          SettingMoneyTransferGeneralDefaultCurrency: $(MoneyTransferGeneralDefaultCurrency)
          SettingMoneyTransferGeneralDefaultType: $(MoneyTransferGeneralDefaultType)
          SettingMoneyTransferMaxRepeatTransferCount: $(MoneyTransferMaxRepeatTransferCount)
          SettingMoneyTransferCorridorsCorporateId: $(MoneyTransferCorridorsCorporateId)
          SettingMoneyTransferMinUserBalanceForRepeatTransfer: $(MoneyTransferMinUserBalanceForRepeatTransfer)
          SettingMoneyTransferReverseOnHoldSchedule: $(MoneyTransferReverseOnHoldSchedule)
          SettingMoneyTransferReverseOnHoldMinNoOfDays: $(MoneyTransferReverseOnHoldMinNoOfDays)
          SettingMoneyTransferRateExpiryInMinutes: $(MoneyTransferRateExpiryInMinutes)
          SettingMoneyTransferRmtCreationSchedule: $(MoneyTransferRmtCreationSchedule)
          SettingMoneyTransferPendingSchedulerMinNoOfDays: $(MoneyTransferPendingSchedulerMinNoOfDays)
          SettingMoneyTransferCheckMinSuspiciousDate: $(MoneyTransferCheckMinSuspiciousDate)
          SettingExperimentNoLoyaltyMaxCountIND: $(ExperimentNoLoyaltyMaxCountIND)
          SettingExperimentNoLoyaltyMaxCountPHL: $(ExperimentNoLoyaltyMaxCountPHL)
          SettingExperimentNoLoyaltyMaxCountNPL: $(ExperimentNoLoyaltyMaxCountNPL)
          SettingEdenredIdentityManagerBaseAddress: $(EdenredIdentityManagerBaseAddress)
          SettingEdenredIdentityManagerAuthority: $(EdenredIdentityManagerAuthority)
          SettingEdenredIdentityManagerResourceId: $(EdenredIdentityManagerResourceId)
          SettingEdenredIdentityManagerClientId: $(EdenredIdentityManagerClientId)
          SettingEdenredIdentityManagerClientSecret: $(EdenredIdentityManagerClientSecret)
          SettingFirebaseCloudMessagingBaseAddress: $(FirebaseCloudMessagingBaseAddress)
          SettingFirebaseCloudMessagingKey: $(FirebaseCloudMessagingKey)
          SettingFirebaseCloudMessagingSenderId: $(FirebaseCloudMessagingSenderId)
          SettingFirebaseCloudMessagingRetryCount: $(FirebaseCloudMessagingRetryCount)
          SettingFirebaseCloudMessagingTimeout: $(FirebaseCloudMessagingTimeout)
          SettingESMOServiceBaseAddress: $(ESMOBaseAddress)
          SettingESMOServiceClientId: $(ESMOClientId)
          SettingESMOServiceClientSecret: $(ESMOClientSecret)
          SettingESMOServiceAuthority: $(ESMOAuthority)
          SettingESMOServiceScope: $(ESMOScope)
          SettingESMOServiceTimeout: $(ESMOTimeout)
          SettingMobileAppHashKey: $(MobileAppHashKey)
          SettingSendGridSenderEmail: $(SendGridSenderEmail)
          SettingSendGridAPIKey: $(SendGridAPIKey)
          SettingSendGridCardHolderRegistrationRejectedEmailTemplateId: $(SendGridCardHolderRegistrationRejectedEmailTemplateId)
          SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId: $(SendGridCardHolderRMTProfileCreatedEmailTemplateId)
          SettingSendGridBankStatementEmailTemplateId: $(SendGridBankStatementEmailTemplateId)
          SettingSendGridStoreOrderPlacedEmailTemplateId: $(SendGridStoreOrderPlacedEmailTemplateId)
          SettingSendGridPortalUserCreatedEmailTemplateId: $(SendGridPortalUserCreatedEmailTemplateId)
          SettingPortalAdminPasswordResetEmailTemplateId: $(PortalAdminPasswordResetEmailTemplateId)
          SettingRedisConnection: $(RedisConnection)
          SettingServiceBusConnection: $(ServiceBusConnection)
          SettingFirstBlackV1PlasticCardId: $(global.FirstBlackV1PlasticCardId)
          SettingFirstBlackV2PlasticCardId: $(global.FirstBlackV2PlasticCardId)
          SettingCleverTapBaseAddress: $(CleverTapBaseAddress)
          SettingCleverTapProjectId: $(CleverTapProjectId)
          SettingCleverTapPassCode: $(CleverTapPassCode)
          SettingExchangeHouseBaseAddress: $(ExchangeHouseBaseAddress)
          SettingExchangeHouseUsername: $(ExchangeHouseUsername)
          SettingExchangeHousePassword: $(ExchangeHousePassword)
          SettingExchangeHouseMaxAllowedBeneficiaryCount: $(ExchangeHouseMaxAllowedBeneficiaryCount)
          SettingExchangeHouseMoneyTransferQueueConnectionString: $(ExchangeHouseMoneyTransferQueueConnectionString)
          SettingExchangeHouseMoneyTransferQueueName: $(ExchangeHouseMoneyTransferQueueName)
          SettingReferralProgramMoneyTransferCountThreshold: $(global.ReferralProgramMoneyTransferCountThreshold)
          SettingReferralProgramMoneyTransferAmountThreshold: $(global.ReferralProgramMoneyTransferAmountThreshold)
          SettingReferralProgramMoneyTransferRewardAmount: $(global.ReferralProgramMoneyTransferRewardAmount)
          SettingReferralProgramMoneyTransferReferralProgramStartDate: $(global.ReferralProgramMoneyTransferReferralProgramStartDate)

          SettingPayrollServiceBaseAddress: $(PayrollServiceBaseAddress)
          SettingPayrollServiceAuthority: $(PayrollServiceAuthority)
          SettingPayrollServiceClientId: $(PayrollServiceClientId)
          SettingPayrollServiceClientSecret: $(PayrollServiceClientSecret)
          SettingPayrollServiceScope: $(PayrollServiceScope)  
          
          SettingHRServiceBaseAddress: $(HRServiceBaseAddress)
          SettingHRServiceAuthority: $(HRServiceAuthority)
          SettingHRServiceClientId: $(HRServiceClientId)
          SettingHRServiceClientSecret: $(HRServiceClientSecret)
          SettingHRServiceScope: $(HRServiceScope)  
          SettingHRServiceCacheInMinutes: $(HRServiceCacheInMinutes)  

          # Step 2: Add your keys here like this: "Setting" + <setting name (same as property)> 
          SettingDirectTransferMaxBeneficiariesCount: $(DirectTransferMaxBeneficiariesCount)
          SettingDirectTransferMinAmountToSend: $(DirectTransferMinAmountToSend)
          SettingDirectTransferMaxAmountToSend: $(DirectTransferMaxAmountToSend)
          SettingDirectTransferMaxAmountToSendPerMonth: $(DirectTransferMaxAmountToSendPerMonth)
          SettingDirectTransferFee: $(DirectTransferFee)
          SettingDirectTransferVAT: $(DirectTransferVAT)
          SettingClaimPendingDirectTransfersQueueConnectionString: $(ClaimPendingDirectTransfersQueueConnectionString)
          SettingClaimPendingDirectTransfersQueueName: $(ClaimPendingDirectTransfersQueueName)
          SettingReversePendingDirectMoneyTransfersSchedule: $(global.ReversePendingDirectMoneyTransfersSchedule)
          SettingReversePendingDirectMoneyTransfersDurationInMin: $(ReversePendingDirectMoneyTransfersDurationInMin)
          SettingReverseFailedDirectMoneyTransfersSchedule: $(global.ReverseFailedDirectMoneyTransfersSchedule)
          ###########################################################################################################

          SettingPaykiiServiceBaseAddress: $(PaykiiServiceBaseAddress)
          SettingPaykiiServiceAPIKey: $(PaykiiServiceAPIKey)
          SettingPaykiiServiceToken: $(PaykiiServiceToken)
          SettingPaykiiServiceCashierId: $(PaykiiServiceCashierId)
          SettingPaykiiServiceCustomerId: $(PaykiiServiceCustomerId)
          SettingPaykiiServiceBillerCatalogUrl: $(PaykiiServiceBillerCatalogUrl)
          SettingPaykiiServiceSKUCatalogUrl: $(PaykiiServiceSKUCatalogUrl)
          SettingPaykiiServiceIOCatalogUrl: $(PaykiiServiceIOCatalogUrl)
          SettingPaykiiServiceAmountDueUrl: $(PaykiiServiceAmountDueUrl)
          SettingPaykiiServiceProcessPaymentUrl: $(PaykiiServiceProcessPaymentUrl)
          SettingPaykiiServiceVerifyPaymentStatusUrl: $(PaykiiServiceVerifyPaymentStatusUrl)
          SettingPaykiiServiceBalanceUrl: $(PaykiiServiceBalanceUrl)
          SettingPaykiiServiceBillNotificationUrl: $(PaykiiServiceBillNotificationUrl)
          SettingPaykiiServiceMobileCarrierLookupUrl: $(PaykiiServiceMobileCarrierLookupUrl)
          SettingPaykiiServiceDailyFXRatePerBillerTypeUrl: $(PaykiiServiceDailyFXRatePerBillerTypeUrl)
          SettingPaykiiServiceBillerFeesCatalogUrl: $(PaykiiServiceBillerFeesCatalogUrl)
          SettingPaykiiServiceRefreshDataIntervalCronExpression: $(PaykiiServiceRefreshDataIntervalCronExpression) 
          SettingPaykiiServiceLocationId: $(PaykiiServiceLocationId)
          SettingPaykiiServicePointOfSaleId: $(PaykiiServicePointOfSaleId)
          SettingBillPaymentPendingBillsRefreshIntervalCronExpression: $(BillPaymentPendingBillsRefreshIntervalCronExpression)
          SettingBillPaymentIconBaseUrl: $(BillPaymentIconBaseUrl) 
          SettingBillPaymentTransactionEnvironment: $(BillPaymentTransactionEnvironment)
          SettingBillPaymentFxRateRefreshIntervalCronExpression: $(BillPaymentFxRateRefreshIntervalCronExpression)
          SettingBillPaymentGridViewDisplayProviderIds: $(BillPaymentGridViewDisplayProviderIds) 
          SettingBillPaymentMaximumLocalBillersLimit: $(BillPaymentMaximumLocalBillersLimit)
          SettingBillPaymentMaximumInternationalBillersLimit: $(BillPaymentMaximumInternationalBillersLimit)
          SettingBillPaymentMaximumAllowedBillAmountPerTransaction: $(BillPaymentMaximumAllowedBillAmountPerTransaction)
          SettingBillPaymentMaximumAllowedBillAmountPerMonth: $(BillPaymentMaximumAllowedBillAmountPerMonth)
          SettingBillPaymentNolProviderCode: $(BillPaymentNolProviderCode)
          SettingBillPaymentAllowedNolAmountForNotVerified: $(BillPaymentAllowedNolAmountForNotVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForVerified: $(BillPaymentNolRechargeMonthlyLimitForVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForNotVerified: $(BillPaymentNolRechargeMonthlyLimitForNotVerified)
          SettingBillPaymentIconContainerName: $(BillPaymentIconContainerName)
          SettingBillPaymentSalikProviderCode: $(BillPaymentSalikProviderCode)
          SettingBillPaymentMockUserId: $(BillPaymentMockUserId) 
          SettingBillPaymentAddBillerQueueName: $(BillPaymentAddBillerQueueName)
          SettingBillPaymentProcessPaymentQueueName: $(BillPaymentProcessPaymentQueueName)
          SettingBillPaymentMockAddBillerQueueName: $(BillPaymentMockAddBillerQueueName)
          SettingBillPaymentMockProcessPaymentQueueName: $(BillPaymentMockProcessPaymentQueueName)
          SettingBillPaymentAmountDueExpireIntervalCronExpression: $(BillPaymentAmountDueExpireIntervalCronExpression)
          SettingBillPaymentAmountDueExpiryHours: $(BillPaymentAmountDueExpiryHours)
          SettingGeneralEmiratesIdStorageURL: $(GeneralEmiratesIdStorageURL)
          SettingGeneralQAUserPhoneNumbers: $(GeneralQAUserPhoneNumbers)
          SettingGeneralIsMiddleNavExperimentActive: $(GeneralIsMiddleNavExperimentActive)
          SettingGeneralIsSecuritySMSAwarenessActive: $(GeneralIsSecuritySMSAwarenessActive)
          SettingGeneralAuthenticationTokenSecretKey: $(GeneralAuthenticationTokenSecretKey)
          SettingGeneralUATPentestPhoneNumbers: $(GeneralUATPentestPhoneNumbers)
          SettingGeneralIsDbSaveRetryEnabled: $(GeneralIsDbSaveRetryEnabled)
          SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled: $(MoneyTransferServiceWUTransactionMinLimitValidationEnabled)
          SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays: $(NonWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays: $(WUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceNonWUCorridors: $(NonWUCorridors)
          SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled: $(RMTStatusFromCreatedToPendingEnabled)
          SettingMoneyTransferServiceLastRaffleWinnerTicketNumber: $(LastRaffleWinnerTicketNumber)
          SettingMoneyTransferServiceLastRaffleWinnerName: $(LastRaffleWinnerName)
          SettingMoneyTransferServiceRaffleDateString: $(RaffleDateString)
          SettingGeneralIsSecuritySMSMigrationActive: $(GeneralIsSecuritySMSMigrationActive)
          SettingGeneralMaxDevicesAllowedForBinding: $(GeneralMaxDevicesAllowedForBinding)
          SettingGeneralDeviceIdBindingPrefix: $(GeneralDeviceIdBindingPrefix)
          SettingGeneralQAAutomationPhoneNumbers: $(GeneralQAAutomationPhoneNumbers)
          SettingGeneralPrimarySmsProvider: $(GeneralPrimarySmsProvider)
          SettingGeneralTestKey: $(GeneralTestKey)
          SettingGeneralEnableRedis: $(GeneralEnableRedis)
          SettingSwaggerUsername: $(SwaggerUsername)
          SettingSwaggerPassword: $(SwaggerPassword)
          SettingEnableSwagger: $(EnableSwagger)
          SettingMultimediaAutoPlayMoneyTransferVideoCorporateIds: $(MultimediaAutoPlayMoneyTransferVideoCorporateIds)

          SettingApiManagementName: $(stage.ApiManagementName)
          DevEmails: $(DevEmails)

          SettingRatingMinimumDaysToShowInApp: $(RatingMinimumDaysToShowInApp)
          SettingRatingMinimumDaysToShowStore: $(RatingMinimumDaysToShowStore)
          SettingStoreEmailRecepients: $(StoreEmailRecepients)
          SettingUnEmploymentInsuranceServiceBusTopicName: $(UnEmploymentInsuranceServiceBusTopicName)
          SettingUnEmploymentInsuranceServiceBusUserTopicName: $(UnEmploymentInsuranceServiceBusUserTopicName)
          SettingUnEmploymentInsuranceServiceBusSubscriptionName: $(UnEmploymentInsuranceServiceBusSubscriptionName)    
          SettingBalanceEnquirySubscriptionServiceBusTopicName: $(BalanceEnquirySubscriptionServiceBusTopicName)
          SettingBalanceEnquirySubscriptionServiceBusSubscriptionName: $(BalanceEnquirySubscriptionServiceBusSubscriptionName)
          SettingAuditTrailServiceBusQueueName: $(AuditTrailServiceBusQueueName)
          SettingDingServiceRetryCount: $(DingServiceRetryCount)
          SettingDingServiceSleepDuration: $(DingServiceSleepDuration)
          SettingDingServiceIsRetryEnabled: $(DingServiceIsRetryEnabled)
          SettingTestingMRDynamicPackageTestNepalNumbers: $(TestingMRDynamicPackageTestNepalNumbers)
          SettingTestingMRInlineFeeCalculationTestNumbers: $(TestingMRInlineFeeCalculationTestNumbers)
          
          SettingRakBankMoneyTransferBaseUrl: $(RakBankMoneyTransferBaseUrl)
          SettingRakBankMoneyTransferUrlPath: $(RakBankMoneyTransferUrlPath)
          SettingRakBankMoneyTransferClientId: $(RakBankMoneyTransferClientId)
          SettingRakBankMoneyTransferClientSecretkey: $(RakBankMoneyTransferClientSecretkey)
          SettingRakBankMoneyTransferSslCertificateName: $(RakBankMoneyTransferSslCertificateName)
          SettingRakBankMoneyTransferSslCertificatePassword: $(RakBankMoneyTransferSslCertificatePassword)
          SettingRakBankMoneyTransferPayloadPrivateKey: $(RakBankMoneyTransferPayloadPrivateKey)
          SettingRakBankMoneyTransferPayloadPublicKey: $(RakBankMoneyTransferPayloadPublicKey)
          SettingRakBankMoneyTransferTokenGrantType: $(RakBankMoneyTransferTokenGrantType)
          SettingRakBankMoneyTransferTokenScope: $(RakBankMoneyTransferTokenScope)
          SettingRakBankMoneyTransferContentType: $(RakBankMoneyTransferContentType)
          SettingRakBankMoneyTransferX509Certificate2Bytes: $(RakBankMoneyTransferX509Certificate2Bytes)
          SettingEncryptionSettingsIsActive: $(EncryptionSettingsIsActive)
          SettingEncryptionSettingsPrivateKey: $(EncryptionSettingsPrivateKey)
          SettingEncryptionSettingsPublicKey: $(EncryptionSettingsPublicKey)
          SettingC3PayPlusMembershipLuckyDrawSchedule: $(C3PayPlusMembershipLuckyDrawSchedule)
          SettingC3PayPlusMembershipLuckyDrawWinnersCount: $(C3PayPlusMembershipLuckyDrawWinnersCount)
          SettingC3PayPlusMembershipCdnUrl: $(C3PayPlusMembershipCdnUrl)
          SettingC3PayPlusMembershipAtmWithdrawalFeeReversalSchedule: $(C3PayPlusMembershipAtmWithdrawalFeeReversalSchedule)
          SettingFirebaseNotificationAuthEndpoint: $(FirebaseNotificationAuthEndpoint)
          SettingFirebaseNotificationBaseUrl: $(FirebaseNotificationBaseUrl)
          SettingFirebaseNotificationSendMethodUrl: $(FirebaseNotificationSendMethodUrl)
          SettingGoogleAuthType: $(GoogleAuthType)
          SettingGoogleAuthProjectId: $(GoogleAuthProjectId)
          SettingGoogleAuthPrivateKeyId: $(GoogleAuthPrivateKeyId)
          SettingGoogleAuthPrivateKey: $(GoogleAuthPrivateKey)
          SettingGoogleAuthClientEmail: $(GoogleAuthClientEmail)
          SettingGoogleAuthClientId: $(GoogleAuthClientId)
          SettingGoogleAuthAuthUri: $(GoogleAuthAuthUri)
          SettingGoogleAuthTokenUri: $(GoogleAuthTokenUri)
          SettingGoogleAuthAuthProviderX509CertUrl: $(GoogleAuthAuthProviderX509CertUrl)
          SettingGoogleAuthClientX509CertUrl: $(GoogleAuthClientX509CertUrl)
          SettingGoogleAuthUniverseDomain: $(GoogleAuthUniverseDomain)
          SettingKycBlockExclusionsShouldBeDeletedAfter: $(KycBlockExclusionsShouldBeDeletedAfter)
          SettingKycBlockExclusionsScheduleTime: $(KycBlockExclusionsScheduleTime)
          SettingC3PayPlusMembershipGenerateTicketsMaxCount: $(C3PayPlusMembershipGenerateTicketsMaxCount)
          SettingC3PayPlusMembershipOverrideLuckyDrawDate: $(C3PayPlusMembershipOverrideLuckyDrawDate)
          SettingC3PayPlusMembershipOverrideLuckyDrawTime: $(C3PayPlusMembershipOverrideLuckyDrawTime)
          SettingC3PayPlusMembershipRenewalSchedule: $(C3PayPlusMembershipRenewalSchedule)
          SettingC3PayPlusMembershipConfirmFirstDebitSchedule: $(C3PayPlusMembershipConfirmFirstDebitSchedule)
          SettingSanctionScreeningApiAddress: $(SanctionScreeningApiAddress)
          SettingRewardServiceBaseAddress: $(RewardServiceBaseAddress)
          SettingRewardServiceResendScheduleTime: $(RewardServiceResendScheduleTime)
          SettingRewardServiceRetryCount: $(RewardServiceRetryCount)
          SettingRewardServiceTimeout: $(RewardServiceTimeout)
          SettingRewardServiceTestAccountUsernames: $(RewardServiceTestAccountUsernames)
          SettingInfobipVoiceCallSettingsBaseUrl: $(InfobipVoiceCallSettingsBaseUrl)
          SettingInfobipVoiceCallSettingsAppKey: $(InfobipVoiceCallSettingsAppKey)
          SettingInfobipVoiceCallSettingsFromNumber: $(InfobipVoiceCallSettingsFromNumber)
          SettingInfobipVoiceCallSettingsCallbackUrl: $(InfobipVoiceCallSettingsCallbackUrl)
          SettingInfobipVoiceCallSettingsCallbackSecret: $(InfobipVoiceCallSettingsCallbackSecret)
          SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule: $(C3PayPlusMembershipFreeMoneyTransferRefundsSchedule)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString: $(C3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName: $(C3PayPlusMembershipATMWithdrawalRefundsQueueName)
          SettingAzureAdInstance: $(SettingAzureAdInstance)
          SettingAzureAdTenantId: $(SettingAzureAdTenantId)
          SettingAzureAdClientId: $(SettingAzureAdClientId)
          SettingAzureAdClientSecret: $(SettingAzureAdClientSecret)
          SettingAzureAdCallbackPath: $(SettingAzureAdCallbackPath)
          SettingAzureAdAudience: $(SettingAzureAdAudience)
          SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck: $(MoneyTransferServiceC3ToC3MinVersionForOtpCheck)
          SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays: $(C3PayPlusMembershipLoginVideoLastSeenIntervalInDays)
          SettingC3PayPlusMembershipTargetedDiscountCooldownDays: $(C3PayPlusMembershipTargetedDiscountCooldownDays)
          SettingC3PayPlusMembershipAllowedPhoneNumbers: $(C3PayPlusMembershipAllowedPhoneNumbers)
          SettingLoginVideoSlotInterval: $(LoginVideoSlotInterval)
          SettingSalaryPaidEventTopicName: $(SalaryPaidEventTopicName)
          SettingSalaryPaidEventSubscriptionName: $(SalaryPaidEventSubscriptionName)
          SettingSalaryPaidEventConnectionString: $(SalaryPaidEventConnectionString)

  - stage: PROD_EAE
    dependsOn: []
    condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/master'))
    variables:
      - name: azureSubscription
        value: "AzureDevops-eae-c3pay-p"
      - name: azureSubscriptionEQ
        value: "AzureDevops-eae-c3pay-p"
      - name: stage.appName
        value: "c3pay2"
      - name: stage.environment
        value: "PROD"
      - name: stage.entityCode
        value: "eae"
      - name: stage.rgName
        value: "c3pay-prod-rg"
      - name: stage.appName
        value: "c3pay2"
      - name: stage.farmSkuName
        value: "S3"
      - name: stage.webJobFarmSkuName
        value: "S3"
      - name: farmSkuCapacity
        value: 3
      - name: farmSkuCapacityMin
        value: 4
      - name: farmSkuCapacityMax
        value: 6
      - name: stage.dbSkuName
        value: 'S6'
      - name: stage.dbWeeklyLtr
        value: "4"
      - name: stage.dbMonthlyLtr
        value: "1"
      - name: stage.identityDbSkuName
        value: "S1"
      - name: stage.serviceBusSkuName
        value: "Premium"
      - name: stage.dbName
        value: "C3Pay.New"

      - name: rakCertificateName
        value: file.rak.prd.pfx
      - name: rakPrivateKeyName
        value: myc3cert2019.key
      - name: rakPublicKeyName
        value: RAKBankPubKey2025.pub
      - group: "C3Pay-prod"
      - name: stage.RAKSFTPInputRootDirectory
        value: "//IN//"
      - name: stage.RAKSFTPOutputRootDirectory
        value: "//OUT//"
      - name: stage.RAKSFTPTransactionStatusDirectory
        value: "RMT//C3TxnStatus//"
      - name: stage.RAKSFTPTransactionBlobContainerName
        value: "raktransaction"
      - name: stage.RAKSFTPProfileStatusDirectory
        value: ""
      - name: stage.MobileRechargeTransactionEnvironment
        value: "LIVE"
      - name: stage.MobileRechargeNonVerifiedLimit
        value: 0
      - name: stage.MobileRechargeVerifiedLimit
        value: 500
      - name: stage.MoneyTransferMonthlyAmountLimit
        value: 30000
      - name: stage.MoneyTransferMonthlyCountLimit
        value: 10
      - name: stage.ApiManagementName
        value: "eae-c3pay-apigw-p"
      - name: stage.ExistingAppServiceName
        value: "none"
      - name: stage.ExistingWebjobServiceName
        value: "eae-c3pay2-secondary-asp-p"

    jobs:
      - template: cd-jobs.yml
        parameters:
          azureSubscription: ${{ variables.azureSubscription }}
          azureSubscriptionEQ: ${{ variables.azureSubscriptionEQ }}
          targetenvironment: "PROD EAE"
          templateLocation: $(global.templateLocation)
          environment: $(stage.environment)
          entityCode: $(stage.entityCode)
          appName: $(stage.appName)
          existingAppServiceName: $(stage.ExistingAppServiceName)
          existingWebjobServiceName: $(stage.ExistingWebjobServiceName)
          location: $(global.location)
          secondaryLocation: $(global.location)
          rgName: $(stage.rgName)
          dbName: $(stage.dbName)
          farmSkuName: $(stage.farmSkuName)
          webJobFarmSkuName: $(stage.webJobFarmSkuName)
          farmSkuCapacity: ${{ variables.farmSkuCapacity }}
          farmSkuCapacityMin: ${{ variables.farmSkuCapacityMin }}
          farmSkuCapacityMax: ${{ variables.farmSkuCapacityMax }}
          isSlotDeploymentEnabled: $(global.SlotDeploymentEnabled)
          slotDeploymentInstanceName: $(global.SlotDeploymentInstanceName)
          dbSkuName: $(stage.dbSkuName)
          dbWeeklyLtr: $(stage.dbWeeklyLtr)
          dbMonthlyLtr: $(stage.dbMonthlyLtr)
          identityDbSkuName: $(stage.identityDbSkuName)
          serviceBusSkuName: $(stage.serviceBusSkuName)
          infraFile: $(global.infraFile)
          webAppBinariesFile: $(global.webAppBinariesFile)
          webJobBinariesFile: $(global.webJobBinariesFile)
          portalWebAppBinariesFile: $(global.portalWebAppBinariesFile)
          sqlFile: $(global.sqlFile)
          identitySqlFile: $(global.identitySqlFile)
          sqlsrvAdministratorLogin: $(sqlsrvAdministratorLogin)
          sqlsrvAdministratorPassword: $(sqlsrvAdministratorPassword)
          DevIP: $(DevIP)
          rakCertificateName: ${{ variables.rakCertificateName }}
          rakCertificatePassword: $(pfxpassword)
          rakPrivateKeyName: ${{ variables.rakPrivateKeyName }}
          rakPublicKeyName: ${{ variables.rakPublicKeyName }}
          SettingEDCAuthority: $(EDConnectAuthority)
          SettingEDCApiName: $(EDConnectApiName)
          SettingEDCApiSecret: $(EDConnectApiSecret)
          SettingPortalEDCAuthority: $(PortalEDConnectAuthority)
          SettingPortalEDCApiName: $(PortalEDConnectApiName)
          SettingPortalEDCApiSecret: $(PortalEDConnectApiSecret)
          SettingAADAuthority: $(AADAuthority)
          SettingAADAudience: $(AADAudience)
          SettingAADAllowedClientIds: $(AADAllowedClientIds)
          SettingKeyName: $(KeyName)
          SettingSignzyURL: $(SignzyURL)
          SettingSignzyFileExchangeAddress: $(SignzyFileExchangeAddress)
          SettingSignzyId: $(SignzyId)
          SettingSignzyUserId: $(SignzyUserId)
          SettingSignzyImageQualityTimeout: $(SignzyImageQualityTimeout)
          SettingSignzyFaceMatchTimeout: $(SignzyFaceMatchTimeout)
          SettingSignzyReadDocumentTimeout: $(SignzyReadDocumentTimeout)
          SettingAzureOCREndpoint: $(AzureOCREndpoint)
          SettingAzureOCRKey: $(AzureOCRKey)
          SettingAzureOCRWaitTimeInMilliSeconds: $(AzureOCRWaitTimeInMilliSeconds)
          SettingAzureFaceEndpoint: $(AzureFaceEndpoint)
          SettingAzureFaceKey: $(AzureFaceKey)
          SettingAzureIdentificationServiceUseAzureOCR: $(AzureIdentificationServiceUseAzureOCR)
          SettingAzureIdentificationServiceUseAzureFace: $(AzureIdentificationServiceUseAzureFace)
          SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck: $(AzureIdentificationServiceMaxDifferenceInDigitsForRecheck)
          SettingKycExpiryCheckStartDate: $(KycExpiryCheckStartDate)
          SettingKycExpiryValidateEmiratesIdExpiryScheduler: $(KycExpiryValidateEmiratesIdExpiryScheduler)
          SettingKycExpiryValidateAdditionKYCScheduler: $(KycExpiryValidateAdditionKYCScheduler)
          SettingTransactionsB2CServiceAuthority: $(TransactionsB2CServiceAuthority) 
          SettingTransactionsB2CServiceClientId: $(TransactionsB2CServiceClientId) 
          SettingTransactionsB2CServiceScope: $(TransactionsB2CServiceScope) 
          SettingTransactionsB2CServiceClientSecret: $(TransactionsB2CServiceClientSecret) 
          SettingTransactionsB2CServiceBaseAddress: $(TransactionsB2CServiceBaseAddress) 
          SettingTransactionsB2CServiceGrantType: $(TransactionsB2CServiceGrantType) 
          SettingTransactionsB2CServiceAPIVersion: $(TransactionsB2CServiceAPIVersion) 
          SettingTransactionsB2CServiceTimeout: $(TransactionsB2CServiceTimeout)
          SettingRakBaseURL: $(RakBaseURL)
          SettingRakClientId: $(RakClientId)
          SettingRakClientSecret: $(RakClientSecret)
          SettingRakProcessBankTransactionsReportsSchedule: $(RakProcessBankTransactionsReportsSchedule)
          SettingRakUpdatePendingBankTransactionsSchedule: $(RakUpdatePendingBankTransactionsSchedule)
          SettingRakReverseFailedBankTransactionsSchedule: $(RakReverseFailedBankTransactionsSchedule)
          SettingRakProcessRakStatementSchedule: $(RakProcessRakStatementSchedule)
          SettingRakProcessRmtSubmissionSchedule: $(RakProcessRmtSubmissionSchedule)
          SettingRakProcessRmtAcknowledgementSchedule: $(RakProcessRmtAcknowledgementSchedule)
          SettingUploadMissingProfilesImagesSchedule: $(UploadMissingProfilesImagesSchedule)
          SettingMoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule: $(MoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule)
          SettingExchangeHouseUpdateStatusSchedule: $(ExchangeHouseUpdateStatusSchedule)
          SettingRakReadRMTProfileResponsesSchedule: $(RakReadRMTProfileResponsesSchedule)
          SettingRakSftpRMTProfileResponsesDirectory: $(RakSftpRMTProfileResponsesDirectory)
          SettingRakSftpMissingRakFileAlertPhoneNumbers: $(RakSftpMissingRakFileAlertPhoneNumbers)
          SettingRakBanksMaxRecords: $(global.RakBanksMaxRecords)
          SettingRakMaxTransactionTriesCount: $(global.RakMaxTransactionTriesCount)
          SettingRakMessageProcessInDelay: $(global.RakMessageProcessInDelay)
          SettingRakTransactionUpdateDelay: $(global.RakTransactionUpdateDelay)
          SettingRakMoneyTransferBeneficiaryDelayInMins: $(global.RakMoneyTransferBeneficiaryDelayInMins)
          SettingRakRmtProfilePositiveStatus: $(RakRmtProfilePositiveStatus)
          SettingRakLoyaltyImplementDate: $(global.RakLoyaltyImplementDate)
          SettingRakLoyaltyLimitCount: $(global.RakLoyaltyLimitCount)
          SettingRakLoyaltyLimitAmount: $(global.RakLoyaltyLimitAmount)
          SettingRakURLPath: $(RakURLPath)
          SettingRakMoneyTransferBeneficiaryCount: $(global.MoneyTransferBeneficiaryCount)
          SettingRakEnableRakTokenCache: $(RakEnableRakTokenCache)
          SettingRakSftpEndPoint: $(RakSftpEndPoint)
          SettingRakSftpPort: $(RakSftpPort)
          SettingRakSftpUsername: $(RakSftpUsername)
          SettingRakSftpPassword: $(RakSftpPassword)
          SettingRAKSFTPInputRootDirectory: $(stage.RAKSFTPInputRootDirectory)
          SettingRAKSFTPOutputRootDirectory: $(stage.RAKSFTPOutputRootDirectory)
          SettingRAKSFTPTransactionStatusDirectory: $(stage.RAKSFTPTransactionStatusDirectory)
          SettingRAKSFTPTransactionBlobContainerName: $(stage.RAKSFTPTransactionBlobContainerName)
          SettingRAKSFTPProfileStatusDirectory: $(stage.RAKSFTPProfileStatusDirectory)
          SettingRAKSFTPBranchesDirectory: $(global.RAKSFTPBranchesDirectory)
          SettingRAKSFTPBranchesArchiveDirectory: $(global.RAKSFTPBranchesArchiveDirectory)
          SettingRakSftpReportDirectory: $(RakSftpReportDirectory)
          SettingRakSftpRmtAcknowledgementDirectory: $(RakSftpRmtAcknowledgementDirectory)
          SettingRakSftpRmtTempSubmissionDirectory: $(RakSftpRmtTempSubmissionDirectory)
          SettingRakRefreshRatesSchedule: $(RakRefreshRatesSchedule)
          SettingExchangeHouseRefreshRatesSchedule: $(ExchangeHouseRefreshRatesSchedule)
          SettingRakRefreshRatesEmiratesId: $(RakRefreshRatesEmiratesId)
          SettingPPSWebAuthBaseURL: $(PPSWebAuthBaseURL)
          SettingPPSWebAuthClientId: $(PPSWebAuthClientId)
          SettingPPSWebAuthClientSecretkey: $(PPSWebAuthClientSecretkey)
          SettingPPSEndpointAddress: $(PPSEndpointAddress)
          SettingPPSUsername: $(PPSUsername)
          SettingPPSPassword: $(PPSPassword)
          SettingPPSSponsorCode: $(PPSSponsorCode)
          SettingPPSCustomerCode: $(PPSCustomerCode)
          SettingPPSSharedSecret: $(PPSSharedSecret)
          SettingPPSTimeout: $(PPSTimeout)
          SettingKYCBaseAddress: $(KYCBaseAddress)
          SettingKYCUsername: $(KYCUsername)
          SettingKYCPassword: $(KYCPassword)
          SettingKYCUniqueRef: $(KYCUniqueRef)
          SettingKYCSponsorCode: $(KYCSponsorCode)
          SettingKYCSharedSecret: $(KYCSharedSecret)
          SettingEtisalatSMSUsername: $(EtisalatSMSUsername)
          SettingEtisalatSMSPassword: $(EtisalatSMSPassword)
          SettingEtisalatSMSBaseAddress: $(EtisalatSMSBaseAddress)
          SettingEtisalatSMSTimeout: $(EtisalatSMSTimeout)
          SettingEtisalatSMSRetryCount: $(EtisalatSMSRetryCount)
          SettingInfobipSMSUsername: $(InfobipSMSUsername)
          SettingInfobipSMSPassword: $(InfobipSMSPassword)
          SettingInfobipSMSBaseAddress: $(InfobipSMSBaseAddress)
          SettingInfobipSMSAuthKey: $(InfobipSMSAuthKey)
          SettingInfobipSMSAuthKeyBaseUrl: $(InfobipSMSAuthKeyBaseUrl)
          SettingInfobipSMSSmsMode: $(InfobipSMSSmsMode)
          SettingInfobipSMSTimeout: $(InfobipSMSTimeout)
          SettingInfobipSMSRetryCount: $(InfobipSMSRetryCount)
          SettingDingBaseURL: $(DingBaseURL)
          SettingDingClientApiKey: $(DingClientApiKey)
          SettingSecondaryDingClientApiKey: $(SecondaryDingClientApiKey)
          SettingMobileRechargeSynchronizeWithDingSchedule: $(global.MobileRechargeSynchronizeWithDingSchedule)
          SettingMobileRechargeUpdateStatusSchedule: $(global.MobileRechargeUpdateStatusSchedule)
          SettingMobileRechargeNickNameLength: $(global.MobileRechargeNickNameLength)
          SettingMobileRechargeC3FeeMode: $(global.MobileRechargeC3FeeMode)
          SettingMobileRechargeMySalaryFeeMode: $(global.MobileRechargeMySalaryFeeMode)
          SettingMobileRechargeSelectedCorporatesWithFee: $(MobileRechargeServiceSelectedCorporatesWithFee)
          SettingMobileRechargeFeeAmount: $(global.MobileRechargeFeeAmount)
          SettingMobileRechargeCallingCardAccountNumberLive: $(global.MobileRechargeCallingCardAccountNumberLive)
          SettingMobileRechargeTransactionEnvironment: $(stage.MobileRechargeTransactionEnvironment)
          SettingMobileRechargeNonVerifiedLimit: $(stage.MobileRechargeNonVerifiedLimit)
          SettingMobileRechargeVerifiedLimit: $(stage.MobileRechargeVerifiedLimit)
          SettingMobileRechargeCustomCallingCardName: $(MobileRechargeCustomCallingCardName)
          SettingMobileRechargeCustomCallingCardCode: $(MobileRechargeCustomCallingCardCode)
          SettingMobileRechargeCustomCallingCardLogoUrl: $(MobileRechargeCustomCallingCardLogoUrl)
          SettingMobileRechargeCustomCallingCardValidationRegex: $(MobileRechargeCustomCallingCardValidationRegex)
          SettingMobileRechargeDynamicPackageSeperator: $(MobileRechargeDynamicPackageSeperator)
          SettingMobileRechargeServiceServiceBusTopicName: $(MobileRechargeServiceServiceBusTopicName)
          SettingMobileRechargeServiceServiceBusSubscriptionName: $(MobileRechargeServiceServiceBusSubscriptionName)
          SettingSurchargeServiceServiceBusTopicName: $(SurchargeServiceServiceBusTopicName)
          SettingMobileRechargeServiceRenewalSchedule: $(MobileRechargeServiceRenewalSchedule)
          SettingMobileRechargeServiceLowBalanceRenewalSchedule: $(MobileRechargeServiceLowBalanceRenewalSchedule)
          SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays: $(MobileRechargeServiceLowBalanceRetryMaxThresholdInDays)
          SettingVpnMembershipRenewalSchedule: $(VpnMembershipRenewalSchedule)
          SettingRenewalCardUpdateServiceBusTopicName: $(RenewalCardUpdateServiceBusTopicName)
          SettingRenewalCardUpdateServiceBusSubscriptionName: $(RenewalCardUpdateServiceBusSubscriptionName)
          SettingMoneyTransferMultimediaURL: $(MoneyTransferMultimediaURL)
          SettingMoneyTransferRefreshBanksAndBranchesSchedule: $(RefreshBanksAndBranchesSchedule)
          SettingMoneyTransferMonthlyAmountLimit: $(stage.MoneyTransferMonthlyAmountLimit)
          SettingMoneyTransferMonthlyCountLimit: $(stage.MoneyTransferMonthlyCountLimit)
          SettingMoneyTransferReversalStartDate: $(MoneyTransferReversalStartDate)
          SettingMoneyTransferReversalMode: $(MoneyTransferReversalMode)
          SettingMoneyTransferFreeTransferExpiryScheduler: $(MoneyTransferFreeTransferExpiryScheduler)
          SettingMoneyTransferRetryBeneficiarySchedule: $(MoneyTransferRetryBeneficiarySchedule)
          SettingMoneyTransferMaxBeneficiaryRetryLimit: $(MoneyTransferMaxBeneficiaryRetryLimit)
          SettingMoneyTransferRetryBeneficiaryDurationInMin: $(MoneyTransferRetryBeneficiaryDurationInMin)
          SettingMoneyTransferEnableRakMock: $(MoneyTransferEnableRakMock)
          SettingMoneyTransferEnableRakNegativeScenarioMock: $(MoneyTransferEnableRakNegativeScenarioMock)
          SettingMoneyTransferComparisonReceiveAmount: $(MoneyTransferComparisonReceiveAmount)
          SettingMoneyTransferComparisonEHTransferFee: $(MoneyTransferComparisonEHTransferFee)
          SettingMoneyTransferComparisonEHRateIncrement: $(MoneyTransferComparisonEHRateIncrement)
          SettingMoneyTransferGWNationalities: $(MoneyTransferGWNationalities)
          SettingMoneyTransferGWLanguages: $(MoneyTransferGWLanguages)
          SettingMoneyTransferGWStartDate: $(MoneyTransferGWStartDate)
          SettingMoneyTransferGeneralDefaultAmount: $(MoneyTransferGeneralDefaultAmount)
          SettingMoneyTransferGeneralDefaultCurrency: $(MoneyTransferGeneralDefaultCurrency)
          SettingMoneyTransferGeneralDefaultType: $(MoneyTransferGeneralDefaultType)
          SettingMoneyTransferMaxRepeatTransferCount: $(MoneyTransferMaxRepeatTransferCount)
          SettingMoneyTransferCorridorsCorporateId: $(MoneyTransferCorridorsCorporateId)
          SettingMoneyTransferMinUserBalanceForRepeatTransfer: $(MoneyTransferMinUserBalanceForRepeatTransfer)
          SettingMoneyTransferReverseOnHoldSchedule: $(MoneyTransferReverseOnHoldSchedule)
          SettingMoneyTransferReverseOnHoldMinNoOfDays: $(MoneyTransferReverseOnHoldMinNoOfDays)
          SettingMoneyTransferRateExpiryInMinutes: $(MoneyTransferRateExpiryInMinutes)
          SettingMoneyTransferRmtCreationSchedule: $(MoneyTransferRmtCreationSchedule)
          SettingMoneyTransferPendingSchedulerMinNoOfDays: $(MoneyTransferPendingSchedulerMinNoOfDays)
          SettingMoneyTransferCheckMinSuspiciousDate: $(MoneyTransferCheckMinSuspiciousDate)
          SettingExperimentNoLoyaltyMaxCountIND: $(ExperimentNoLoyaltyMaxCountIND)
          SettingExperimentNoLoyaltyMaxCountPHL: $(ExperimentNoLoyaltyMaxCountPHL)
          SettingExperimentNoLoyaltyMaxCountNPL: $(ExperimentNoLoyaltyMaxCountNPL)
          SettingEdenredIdentityManagerBaseAddress: $(EdenredIdentityManagerBaseAddress)
          SettingEdenredIdentityManagerAuthority: $(EdenredIdentityManagerAuthority)
          SettingEdenredIdentityManagerResourceId: $(EdenredIdentityManagerResourceId)
          SettingEdenredIdentityManagerClientId: $(EdenredIdentityManagerClientId)
          SettingEdenredIdentityManagerClientSecret: $(EdenredIdentityManagerClientSecret)
          SettingFirebaseCloudMessagingBaseAddress: $(FirebaseCloudMessagingBaseAddress)
          SettingFirebaseCloudMessagingKey: $(FirebaseCloudMessagingKey)
          SettingFirebaseCloudMessagingSenderId: $(FirebaseCloudMessagingSenderId)
          SettingFirebaseCloudMessagingRetryCount: $(FirebaseCloudMessagingRetryCount)
          SettingFirebaseCloudMessagingTimeout: $(FirebaseCloudMessagingTimeout)
          SettingESMOServiceBaseAddress: $(ESMOBaseAddress)
          SettingESMOServiceClientId: $(ESMOClientId)
          SettingESMOServiceClientSecret: $(ESMOClientSecret)
          SettingESMOServiceAuthority: $(ESMOAuthority)
          SettingESMOServiceScope: $(ESMOScope)
          SettingESMOServiceTimeout: $(ESMOTimeout)
          SettingMobileAppHashKey: $(MobileAppHashKey)
          SettingSendGridSenderEmail: $(SendGridSenderEmail)
          SettingSendGridAPIKey: $(SendGridAPIKey)
          SettingSendGridCardHolderRegistrationRejectedEmailTemplateId: $(SendGridCardHolderRegistrationRejectedEmailTemplateId)
          SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId: $(SendGridCardHolderRMTProfileCreatedEmailTemplateId)
          SettingSendGridBankStatementEmailTemplateId: $(SendGridBankStatementEmailTemplateId)
          SettingSendGridStoreOrderPlacedEmailTemplateId: $(SendGridStoreOrderPlacedEmailTemplateId)
          SettingSendGridPortalUserCreatedEmailTemplateId: $(SendGridPortalUserCreatedEmailTemplateId)
          SettingPortalAdminPasswordResetEmailTemplateId: $(PortalAdminPasswordResetEmailTemplateId)
          SettingRedisConnection: $(RedisConnection)
          SettingServiceBusConnection: $(ServiceBusConnection)
          SettingFirstBlackV1PlasticCardId: $(global.FirstBlackV1PlasticCardId)
          SettingFirstBlackV2PlasticCardId: $(global.FirstBlackV2PlasticCardId)
          SettingCleverTapBaseAddress: $(CleverTapBaseAddress)
          SettingCleverTapProjectId: $(CleverTapProjectId)
          SettingCleverTapPassCode: $(CleverTapPassCode)
          SettingExchangeHouseBaseAddress: $(ExchangeHouseBaseAddress)
          SettingExchangeHouseUsername: $(ExchangeHouseUsername)
          SettingExchangeHousePassword: $(ExchangeHousePassword)
          SettingExchangeHouseMaxAllowedBeneficiaryCount: $(ExchangeHouseMaxAllowedBeneficiaryCount)
          SettingExchangeHouseMoneyTransferQueueConnectionString: $(ExchangeHouseMoneyTransferQueueConnectionString)
          SettingExchangeHouseMoneyTransferQueueName: $(ExchangeHouseMoneyTransferQueueName)
          SettingReferralProgramMoneyTransferCountThreshold: $(global.ReferralProgramMoneyTransferCountThreshold)
          SettingReferralProgramMoneyTransferAmountThreshold: $(global.ReferralProgramMoneyTransferAmountThreshold)
          SettingReferralProgramMoneyTransferRewardAmount: $(global.ReferralProgramMoneyTransferRewardAmount)
          SettingReferralProgramMoneyTransferReferralProgramStartDate: $(global.ReferralProgramMoneyTransferReferralProgramStartDate)

          SettingPayrollServiceBaseAddress: $(PayrollServiceBaseAddress)
          SettingPayrollServiceAuthority: $(PayrollServiceAuthority)
          SettingPayrollServiceClientId: $(PayrollServiceClientId)
          SettingPayrollServiceClientSecret: $(PayrollServiceClientSecret)
          SettingPayrollServiceScope: $(PayrollServiceScope)   
          
          SettingHRServiceBaseAddress: $(HRServiceBaseAddress)
          SettingHRServiceAuthority: $(HRServiceAuthority)
          SettingHRServiceClientId: $(HRServiceClientId)
          SettingHRServiceClientSecret: $(HRServiceClientSecret)
          SettingHRServiceScope: $(HRServiceScope)  
          SettingHRServiceCacheInMinutes: $(HRServiceCacheInMinutes)  

          # Step 3: Add your keys here like this: "Setting" + <setting name (same as property)> 
          SettingDirectTransferMaxBeneficiariesCount: $(DirectTransferMaxBeneficiariesCount)
          SettingDirectTransferMinAmountToSend: $(DirectTransferMinAmountToSend)
          SettingDirectTransferMaxAmountToSend: $(DirectTransferMaxAmountToSend)
          SettingDirectTransferMaxAmountToSendPerMonth: $(DirectTransferMaxAmountToSendPerMonth)
          SettingDirectTransferFee: $(DirectTransferFee)
          SettingDirectTransferVAT: $(DirectTransferVAT)
          SettingClaimPendingDirectTransfersQueueConnectionString: $(ClaimPendingDirectTransfersQueueConnectionString)
          SettingClaimPendingDirectTransfersQueueName: $(ClaimPendingDirectTransfersQueueName)
          SettingReversePendingDirectMoneyTransfersSchedule: $(global.ReversePendingDirectMoneyTransfersSchedule)
          SettingReversePendingDirectMoneyTransfersDurationInMin: $(ReversePendingDirectMoneyTransfersDurationInMin)
          SettingReverseFailedDirectMoneyTransfersSchedule: $(global.ReverseFailedDirectMoneyTransfersSchedule)
          ###########################################################################################################


          SettingPaykiiServiceBaseAddress: $(PaykiiServiceBaseAddress)
          SettingPaykiiServiceAPIKey: $(PaykiiServiceAPIKey)
          SettingPaykiiServiceToken: $(PaykiiServiceToken)
          SettingPaykiiServiceCashierId: $(PaykiiServiceCashierId)
          SettingPaykiiServiceCustomerId: $(PaykiiServiceCustomerId)
          SettingPaykiiServiceBillerCatalogUrl: $(PaykiiServiceBillerCatalogUrl)
          SettingPaykiiServiceSKUCatalogUrl: $(PaykiiServiceSKUCatalogUrl)
          SettingPaykiiServiceIOCatalogUrl: $(PaykiiServiceIOCatalogUrl)
          SettingPaykiiServiceAmountDueUrl: $(PaykiiServiceAmountDueUrl)
          SettingPaykiiServiceProcessPaymentUrl: $(PaykiiServiceProcessPaymentUrl)
          SettingPaykiiServiceVerifyPaymentStatusUrl: $(PaykiiServiceVerifyPaymentStatusUrl)
          SettingPaykiiServiceBalanceUrl: $(PaykiiServiceBalanceUrl)
          SettingPaykiiServiceBillNotificationUrl: $(PaykiiServiceBillNotificationUrl)
          SettingPaykiiServiceMobileCarrierLookupUrl: $(PaykiiServiceMobileCarrierLookupUrl)
          SettingPaykiiServiceDailyFXRatePerBillerTypeUrl: $(PaykiiServiceDailyFXRatePerBillerTypeUrl)
          SettingPaykiiServiceBillerFeesCatalogUrl: $(PaykiiServiceBillerFeesCatalogUrl)
          SettingPaykiiServiceRefreshDataIntervalCronExpression: $(PaykiiServiceRefreshDataIntervalCronExpression) 
          SettingPaykiiServiceLocationId: $(PaykiiServiceLocationId)
          SettingPaykiiServicePointOfSaleId: $(PaykiiServicePointOfSaleId)
          SettingBillPaymentPendingBillsRefreshIntervalCronExpression: $(BillPaymentPendingBillsRefreshIntervalCronExpression)
          SettingBillPaymentIconBaseUrl: $(BillPaymentIconBaseUrl)
          SettingBillPaymentTransactionEnvironment: $(BillPaymentTransactionEnvironment)
          SettingBillPaymentFxRateRefreshIntervalCronExpression: $(BillPaymentFxRateRefreshIntervalCronExpression)
          SettingBillPaymentGridViewDisplayProviderIds: $(BillPaymentGridViewDisplayProviderIds)
          SettingBillPaymentMaximumLocalBillersLimit: $(BillPaymentMaximumLocalBillersLimit)
          SettingBillPaymentMaximumInternationalBillersLimit: $(BillPaymentMaximumInternationalBillersLimit)
          SettingBillPaymentMaximumAllowedBillAmountPerTransaction: $(BillPaymentMaximumAllowedBillAmountPerTransaction)
          SettingBillPaymentMaximumAllowedBillAmountPerMonth: $(BillPaymentMaximumAllowedBillAmountPerMonth)
          SettingBillPaymentNolProviderCode: $(BillPaymentNolProviderCode)
          SettingBillPaymentAllowedNolAmountForNotVerified: $(BillPaymentAllowedNolAmountForNotVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForVerified: $(BillPaymentNolRechargeMonthlyLimitForVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForNotVerified: $(BillPaymentNolRechargeMonthlyLimitForNotVerified)
          SettingBillPaymentIconContainerName: $(BillPaymentIconContainerName)
          SettingBillPaymentSalikProviderCode: $(BillPaymentSalikProviderCode)
          SettingBillPaymentMockUserId: $(BillPaymentMockUserId) 
          SettingBillPaymentAddBillerQueueName: $(BillPaymentAddBillerQueueName)
          SettingBillPaymentProcessPaymentQueueName: $(BillPaymentProcessPaymentQueueName)
          SettingBillPaymentMockAddBillerQueueName: $(BillPaymentMockAddBillerQueueName)
          SettingBillPaymentMockProcessPaymentQueueName: $(BillPaymentMockProcessPaymentQueueName)
          SettingBillPaymentAmountDueExpireIntervalCronExpression: $(BillPaymentAmountDueExpireIntervalCronExpression)
          SettingBillPaymentAmountDueExpiryHours: $(BillPaymentAmountDueExpiryHours)
          SettingGeneralEmiratesIdStorageURL: $(GeneralEmiratesIdStorageURL)
          SettingGeneralQAUserPhoneNumbers: $(GeneralQAUserPhoneNumbers)
          SettingGeneralIsMiddleNavExperimentActive: $(GeneralIsMiddleNavExperimentActive)
          SettingGeneralIsSecuritySMSAwarenessActive: $(GeneralIsSecuritySMSAwarenessActive)
          SettingGeneralAuthenticationTokenSecretKey: $(GeneralAuthenticationTokenSecretKey)
          SettingGeneralUATPentestPhoneNumbers: $(GeneralUATPentestPhoneNumbers)
          SettingGeneralIsDbSaveRetryEnabled: $(GeneralIsDbSaveRetryEnabled)
          SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled: $(MoneyTransferServiceWUTransactionMinLimitValidationEnabled)
          SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays: $(NonWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays: $(WUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceNonWUCorridors: $(NonWUCorridors)
          SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled: $(RMTStatusFromCreatedToPendingEnabled)
          SettingMoneyTransferServiceLastRaffleWinnerTicketNumber: $(LastRaffleWinnerTicketNumber)
          SettingMoneyTransferServiceLastRaffleWinnerName: $(LastRaffleWinnerName)
          SettingMoneyTransferServiceRaffleDateString: $(RaffleDateString)
          SettingGeneralIsSecuritySMSMigrationActive: $(GeneralIsSecuritySMSMigrationActive)
          SettingGeneralMaxDevicesAllowedForBinding: $(GeneralMaxDevicesAllowedForBinding)
          SettingGeneralDeviceIdBindingPrefix: $(GeneralDeviceIdBindingPrefix)
          SettingGeneralQAAutomationPhoneNumbers: $(GeneralQAAutomationPhoneNumbers)
          SettingGeneralPrimarySmsProvider: $(GeneralPrimarySmsProvider)
          SettingGeneralTestKey: $(GeneralTestKey)
          SettingGeneralEnableRedis: $(GeneralEnableRedis)
          SettingSwaggerUsername: $(SwaggerUsername)
          SettingSwaggerPassword: $(SwaggerPassword)
          SettingEnableSwagger: $(EnableSwagger)
          SettingMultimediaAutoPlayMoneyTransferVideoCorporateIds: $(MultimediaAutoPlayMoneyTransferVideoCorporateIds)

          SettingApiManagementName: $(stage.ApiManagementName)
          DevEmails: $(DevEmails)

          SettingRatingMinimumDaysToShowInApp: $(RatingMinimumDaysToShowInApp)
          SettingRatingMinimumDaysToShowStore: $(RatingMinimumDaysToShowStore) 
          
          SettingStoreEmailRecepients: $(StoreEmailRecepients)
          SettingUnEmploymentInsuranceServiceBusTopicName: $(UnEmploymentInsuranceServiceBusTopicName)
          SettingUnEmploymentInsuranceServiceBusUserTopicName: $(UnEmploymentInsuranceServiceBusUserTopicName)
          SettingUnEmploymentInsuranceServiceBusSubscriptionName: $(UnEmploymentInsuranceServiceBusSubscriptionName)
          SettingBalanceEnquirySubscriptionServiceBusTopicName: $(BalanceEnquirySubscriptionServiceBusTopicName)
          SettingBalanceEnquirySubscriptionServiceBusSubscriptionName: $(BalanceEnquirySubscriptionServiceBusSubscriptionName)
          SettingAuditTrailServiceBusQueueName: $(AuditTrailServiceBusQueueName)
          SettingDingServiceRetryCount: $(DingServiceRetryCount)
          SettingDingServiceSleepDuration: $(DingServiceSleepDuration)
          SettingDingServiceIsRetryEnabled: $(DingServiceIsRetryEnabled)
          SettingTestingMRDynamicPackageTestNepalNumbers: $(TestingMRDynamicPackageTestNepalNumbers)
          SettingTestingMRInlineFeeCalculationTestNumbers: $(TestingMRInlineFeeCalculationTestNumbers)

          SettingRakBankMoneyTransferBaseUrl: $(RakBankMoneyTransferBaseUrl)
          SettingRakBankMoneyTransferUrlPath: $(RakBankMoneyTransferUrlPath)
          SettingRakBankMoneyTransferClientId: $(RakBankMoneyTransferClientId)
          SettingRakBankMoneyTransferClientSecretkey: $(RakBankMoneyTransferClientSecretkey)
          SettingRakBankMoneyTransferSslCertificateName: $(RakBankMoneyTransferSslCertificateName)
          SettingRakBankMoneyTransferSslCertificatePassword: $(RakBankMoneyTransferSslCertificatePassword)
          SettingRakBankMoneyTransferPayloadPrivateKey: $(RakBankMoneyTransferPayloadPrivateKey)
          SettingRakBankMoneyTransferPayloadPublicKey: $(RakBankMoneyTransferPayloadPublicKey)
          SettingRakBankMoneyTransferTokenGrantType: $(RakBankMoneyTransferTokenGrantType)
          SettingRakBankMoneyTransferTokenScope: $(RakBankMoneyTransferTokenScope)
          SettingRakBankMoneyTransferContentType: $(RakBankMoneyTransferContentType)
          SettingRakBankMoneyTransferX509Certificate2Bytes: $(RakBankMoneyTransferX509Certificate2Bytes) 
          SettingEncryptionSettingsIsActive: $(EncryptionSettingsIsActive)
          SettingEncryptionSettingsPrivateKey: $(EncryptionSettingsPrivateKey)
          SettingEncryptionSettingsPublicKey: $(EncryptionSettingsPublicKey)
          SettingC3PayPlusMembershipLuckyDrawSchedule: $(C3PayPlusMembershipLuckyDrawSchedule)
          SettingC3PayPlusMembershipLuckyDrawWinnersCount: $(C3PayPlusMembershipLuckyDrawWinnersCount)
          SettingC3PayPlusMembershipCdnUrl: $(C3PayPlusMembershipCdnUrl)
          SettingC3PayPlusMembershipAtmWithdrawalFeeReversalSchedule: $(C3PayPlusMembershipAtmWithdrawalFeeReversalSchedule)
          SettingFirebaseNotificationAuthEndpoint: $(FirebaseNotificationAuthEndpoint)
          SettingFirebaseNotificationBaseUrl: $(FirebaseNotificationBaseUrl)
          SettingFirebaseNotificationSendMethodUrl: $(FirebaseNotificationSendMethodUrl)
          SettingGoogleAuthType: $(GoogleAuthType)
          SettingGoogleAuthProjectId: $(GoogleAuthProjectId)
          SettingGoogleAuthPrivateKeyId: $(GoogleAuthPrivateKeyId)
          SettingGoogleAuthPrivateKey: $(GoogleAuthPrivateKey)
          SettingGoogleAuthClientEmail: $(GoogleAuthClientEmail)
          SettingGoogleAuthClientId: $(GoogleAuthClientId)
          SettingGoogleAuthAuthUri: $(GoogleAuthAuthUri)
          SettingGoogleAuthTokenUri: $(GoogleAuthTokenUri)
          SettingGoogleAuthAuthProviderX509CertUrl: $(GoogleAuthAuthProviderX509CertUrl)
          SettingGoogleAuthClientX509CertUrl: $(GoogleAuthClientX509CertUrl)
          SettingGoogleAuthUniverseDomain: $(GoogleAuthUniverseDomain)
          SettingKycBlockExclusionsShouldBeDeletedAfter: $(KycBlockExclusionsShouldBeDeletedAfter)
          SettingKycBlockExclusionsScheduleTime: $(KycBlockExclusionsScheduleTime)
          SettingC3PayPlusMembershipGenerateTicketsMaxCount: $(C3PayPlusMembershipGenerateTicketsMaxCount)
          SettingC3PayPlusMembershipOverrideLuckyDrawDate: $(C3PayPlusMembershipOverrideLuckyDrawDate)
          SettingC3PayPlusMembershipOverrideLuckyDrawTime: $(C3PayPlusMembershipOverrideLuckyDrawTime)
          SettingC3PayPlusMembershipRenewalSchedule: $(C3PayPlusMembershipRenewalSchedule)
          SettingC3PayPlusMembershipConfirmFirstDebitSchedule: $(C3PayPlusMembershipConfirmFirstDebitSchedule)
          SettingSanctionScreeningApiAddress: $(SanctionScreeningApiAddress)
          SettingRewardServiceBaseAddress: $(RewardServiceBaseAddress)
          SettingRewardServiceResendScheduleTime: $(RewardServiceResendScheduleTime)
          SettingRewardServiceRetryCount: $(RewardServiceRetryCount)
          SettingRewardServiceTimeout: $(RewardServiceTimeout)
          SettingRewardServiceTestAccountUsernames: $(RewardServiceTestAccountUsernames)
          SettingInfobipVoiceCallSettingsBaseUrl: $(InfobipVoiceCallSettingsBaseUrl)
          SettingInfobipVoiceCallSettingsAppKey: $(InfobipVoiceCallSettingsAppKey)
          SettingInfobipVoiceCallSettingsFromNumber: $(InfobipVoiceCallSettingsFromNumber)
          SettingInfobipVoiceCallSettingsCallbackUrl: $(InfobipVoiceCallSettingsCallbackUrl)
          SettingInfobipVoiceCallSettingsCallbackSecret: $(InfobipVoiceCallSettingsCallbackSecret)
          SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule: $(C3PayPlusMembershipFreeMoneyTransferRefundsSchedule)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString: $(C3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName: $(C3PayPlusMembershipATMWithdrawalRefundsQueueName)
          SettingAzureAdInstance: $(SettingAzureAdInstance)
          SettingAzureAdTenantId: $(SettingAzureAdTenantId)
          SettingAzureAdClientId: $(SettingAzureAdClientId)
          SettingAzureAdClientSecret: $(SettingAzureAdClientSecret)
          SettingAzureAdCallbackPath: $(SettingAzureAdCallbackPath)
          SettingAzureAdAudience: $(SettingAzureAdAudience)
          SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck: $(MoneyTransferServiceC3ToC3MinVersionForOtpCheck)
          SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays: $(C3PayPlusMembershipLoginVideoLastSeenIntervalInDays)
          SettingC3PayPlusMembershipTargetedDiscountCooldownDays: $(C3PayPlusMembershipTargetedDiscountCooldownDays)
          SettingC3PayPlusMembershipAllowedPhoneNumbers: $(C3PayPlusMembershipAllowedPhoneNumbers)
          SettingLoginVideoSlotInterval: $(LoginVideoSlotInterval)
          SettingSalaryPaidEventTopicName: $(SalaryPaidEventTopicName)
          SettingSalaryPaidEventSubscriptionName: $(SalaryPaidEventSubscriptionName)
          SettingSalaryPaidEventConnectionString: $(SalaryPaidEventConnectionString)