﻿using AutoMapper;
using C3Pay.API.Resources;
using C3Pay.API.Resources.Login;
using C3Pay.API.Resources.UserProfile;
using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Services.Commands;
using C3Pay.Services.Handlers;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]")]
    [InputValidationAttribute]
    [ApiController]
    public class LoginController : ControllerBase
    {
        private readonly ITableStorageService _tableStorageService;
        private readonly IMapper _mapper;
        private readonly UserCommandHandler _userCommandHandler;
        private readonly GeneralSettings _generalSettings;
        private readonly string _otpStorageTableName = "otp";
        private readonly ILookupService _lookupService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IUserService _userService;
        private readonly IFeatureManager _featureManager;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userCommandHandler"></param>
        /// <param name="tableStorageService"></param>
        /// <param name="generalSettings"></param>
        public LoginController(UserCommandHandler userCommandHandler, ITableStorageService tableStorageService, IOptions<GeneralSettings> generalSettings, IMapper mapper, ILookupService lookupService, IHttpContextAccessor httpContextAccessor, IUserService userService, IFeatureManager featureManager)
        {
            _userCommandHandler = userCommandHandler;
            _tableStorageService = tableStorageService;
            _generalSettings = generalSettings.Value;
            _mapper = mapper;
            _lookupService = lookupService;
            _httpContextAccessor = httpContextAccessor;
            _userService = userService;
            _featureManager = featureManager;
        }

        #region Forgot Password
        /// <summary>
        /// ForgotPasswordVerifyPin V1
        /// </summary>
        /// <param name="verifyForgotPasswordDetailsRequest"></param>
        /// <returns></returns>
        [HttpPost("forgot-password/verification/pin/v2")]
        public async Task<ActionResult<Guid>> ForgotPasswordVerifyPinV2(VerifyLoggedOutCardRequestDto verifyForgotPasswordDetailsRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var verifyPhoneResult = await this._userCommandHandler.Handle(new CheckResetPasswordEligibilityCommand()
            {
                PhoneNumber = verifyForgotPasswordDetailsRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            var eligibility = verifyPhoneResult.Data.Eligibility;

            if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForForgotPassword)
            {
                if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForAutoUnblock)
                {
                    eligibility = BaseEnums.ResetPasswordFlowEligibilityResult.InEligible;
                }

                return this.BadRequest(eligibility.ToString());
            }

            var command = this._mapper.Map<VerifyUserCardCommand>(verifyForgotPasswordDetailsRequest);

            var verifyDetailsResult = await this._userCommandHandler.Handle(command);

            if (!verifyDetailsResult.IsSuccessful)
            {
                return this.BadRequest(verifyDetailsResult.ErrorMessage);
            }

            return this.Ok(VerifyAtmPinResult.PinVerified.ToString());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="updatePasswordRequest"></param>
        /// <returns></returns>
        [HttpPut("forgot-password/password")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> ForgotPasswordUpdatePassword(UpdateUserRequestDto updatePasswordRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var updatePasswordResult = await this._userCommandHandler.Handle(new UpdateUserPasswordCommand()
            {
                UserId = updatePasswordRequest.UserId,
                Password = updatePasswordRequest.Password,
                PhoneNumber = updatePasswordRequest.PhoneNumber
            });

            if (!updatePasswordResult.IsSuccessful)
            {
                return BadRequest(updatePasswordResult.ErrorMessage);
            }

            return this.Ok();
        }


        [HttpGet("forgot-password/video")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> ForgotPasswordGetVideos()
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var forgotPasswordvideoResult = await _lookupService.GetMultimediaResources(feature: (int)FeatureType.ForgotPassword).ConfigureAwait(false);
            return this.Ok(forgotPasswordvideoResult.Data
                 .Select(resource => new ForgotPasswordVideoResponseDto
                 {
                     LanguageCode = resource.Language,
                     Url = resource.Url,
                     IsDefault = resource.Language == "en"
                 })
                 .ToList());
        }

        #endregion

        /// <summary>
        /// Possible results: ForgotPassword, AutoUnblock, AutoUnblockMaxAttemptsReached, AutoUnblockDisabled
        /// </summary>
        /// <param name="verifyPhoneNumberRequest"></param>
        /// <returns></returns>
        [HttpPost("reset-password/flow-eligibility")]
        public async Task<ActionResult<CheckResetPasswordEligbilityResponseDto>> CheckResetPasswordEligibility(VerifyPhoneNumberRequestDto verifyPhoneNumberRequest)
        {
            var verifyPhoneResult = await this._userCommandHandler.Handle(new CheckResetPasswordEligibilityCommand()
            {
                PhoneNumber = verifyPhoneNumberRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            var mappedResult = this._mapper.Map<CheckResetPasswordEligbilityResponseDto>(verifyPhoneResult.Data);

            return this.Ok(mappedResult);
        }

        /// <summary>
        /// Possible results: ForgotPassword, AutoUnblock, AutoUnblockMaxAttemptsReached, AutoUnblockDisabled
        /// </summary>
        /// <param name="verifyPhoneNumberRequest"></param>
        /// <returns></returns>
        [HttpPost("reset-password/flow-eligibility/v2")]
        public async Task<ActionResult<CheckResetPasswordEligbilityResponseDto>> CheckResetPasswordEligibilityV2(VerifyPhoneNumberRequestDto verifyPhoneNumberRequest)
        {
            var verifyPhoneResult = await this._userCommandHandler.Handle(new CheckResetPasswordEligibilityCommand()
            {
                PhoneNumber = verifyPhoneNumberRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            var mappedResult = this._mapper.Map<CheckResetPasswordEligbilityResponseDto>(verifyPhoneResult.Data);
            mappedResult.UserId = new Guid();

            return this.Ok(mappedResult);
        }

        /// <summary>
        /// Possible results: ForgotPassword, AutoUnblock, AutoUnblockMaxAttemptsReached, AutoUnblockDisabled
        /// </summary>
        /// <param name="verifyPhoneNumberRequest"></param>
        /// <returns></returns>
        [HttpPost("reset-password/flow-eligibility/v3")]
        public async Task<ActionResult<CheckResetPasswordEligbilityResponseDto>> CheckResetPasswordEligibilityV3(VerifyPhoneNumberRequestDto verifyPhoneNumberRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var verifyPhoneResult = await this._userCommandHandler.Handle(new CheckResetPasswordEligibilityCommand()
            {
                PhoneNumber = verifyPhoneNumberRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            var mappedResult = this._mapper.Map<CheckResetPasswordEligbilityResponseDto>(verifyPhoneResult.Data);
            mappedResult.UserId = new Guid();

            return this.Ok(mappedResult);
        }

        #region AutoUnblock


        /// <summary>
        /// Possible Returns 400-Error, 400-WrongCombination, 400-MaxAttemptsReached, 200-UserId
        /// </summary>
        /// <param name="verifyAutoUnblockDetailsRequest"></param>
        /// <returns></returns>
        [HttpPost("auto-unblock/verify/v2")]
        public async Task<ActionResult<Guid>> AutoUnblockVerifyDetailsV2(VerifyAutoUnblockDetailsByCardRequestDto verifyAutoUnblockDetailsRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var verifyPhoneResult = await this._userCommandHandler.Handle(new CheckResetPasswordEligibilityCommand()
            {
                PhoneNumber = verifyAutoUnblockDetailsRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            var eligibility = verifyPhoneResult.Data.Eligibility;

            if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForAutoUnblock)
            {
                if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForForgotPassword)
                {
                    eligibility = BaseEnums.ResetPasswordFlowEligibilityResult.InEligible;
                }

                return this.BadRequest(eligibility.ToString());
            }

            var command = this._mapper.Map<VerifyAutoUnblockDetailsByCardCommand>(verifyAutoUnblockDetailsRequest);

            command.ApplicationId = Core.BaseEnums.MobileApplicationId.C3Pay;

            var verifyDetailsResult = await this._userCommandHandler.Handle(command);

            if (!verifyDetailsResult.IsSuccessful)
            {
                return this.BadRequest(verifyDetailsResult.ErrorMessage);
            }

            return this.Ok(verifyDetailsResult.Data);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="updatePasswordRequest"></param>
        /// <returns></returns>
        [HttpPut("auto-unblock/password")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> AutoUnblockUpdatePassword(UpdateUserRequestDto updatePasswordRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var updatePasswordResult = await this._userCommandHandler.Handle(new UpdateUserPasswordCommand()
            {
                UserId = updatePasswordRequest.UserId,
                Password = updatePasswordRequest.Password,
                UnlockAccount = true
            });

            if (!updatePasswordResult.IsSuccessful)
            {
                return BadRequest(updatePasswordResult.ErrorMessage);
            }

            return this.Ok();
        }

        #endregion

        #region Depricated

        /// <summary>
        /// Verifies secret answers. possible returns: 400-WrongQuestion, 400-WrongAnswer, 200-Verified
        /// </summary>
        /// <param name="verifySecretAnswerRequest"></param>
        /// <returns></returns>
        [HttpPost("auto-unblock/verification/secret-answer")]
        public async Task<ActionResult> AutoUnblockVerifyUserSecretAnswer(VerifyUserSecretAnswerRequestDto verifySecretAnswerRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var validateSecretAnswerResult = await this._userCommandHandler.Handle(new VerifyUserSecretAnswerCommand()
            {
                SecretAnswer = verifySecretAnswerRequest.SecretAnswer,
                SecurityQuestionId = verifySecretAnswerRequest.SecurityQuestionId,
                UserId = verifySecretAnswerRequest.UserId
            });

            if (!validateSecretAnswerResult.IsSuccessful)
            {
                return BadRequest(validateSecretAnswerResult.ErrorMessage);
            }

            if (validateSecretAnswerResult.Data != BaseEnums.SecretAnswerVerificationResult.Verified)
            {
                return BadRequest(validateSecretAnswerResult.Data.ToString());
            }

            return this.Ok(validateSecretAnswerResult.Data.ToString());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet("auto-unblock/{userId}/security-questions")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> AutoUnblockGetUserSecurityQuestions(Guid userId)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var getUserQuestionsResult = await this._userCommandHandler.Handle(new GetUserSecurityQuestionsCommand()
            {
                UserId = userId
            });

            if (!getUserQuestionsResult.IsSuccessful)
            {
                return BadRequest(getUserQuestionsResult.ErrorMessage);
            }

            return this.Ok(getUserQuestionsResult.Data);
        }

        /// <summary>
        /// Possible Returns 400-Error, 400-WrongCombination, 400-MaxAttemptsReached, 200-UserId
        /// </summary>
        /// <param name="verifyAutoUnblockDetailsRequest"></param>
        /// <returns></returns>
        [HttpPost("auto-unblock/verify")]
        public async Task<ActionResult<Guid>> AutoUnblockVerifyDetails(VerifyAutoUnblockDetailsRequestDto verifyAutoUnblockDetailsRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var verifyPhoneResult = await this._userCommandHandler.Handle(new CheckResetPasswordEligibilityCommand()
            {
                PhoneNumber = verifyAutoUnblockDetailsRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            var eligibility = verifyPhoneResult.Data.Eligibility;

            if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForAutoUnblock)
            {
                if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForForgotPassword)
                {
                    eligibility = BaseEnums.ResetPasswordFlowEligibilityResult.InEligible;
                }

                return this.BadRequest(eligibility.ToString());
            }

            var command = this._mapper.Map<VerifyAutoUnblockDetailsCommand>(verifyAutoUnblockDetailsRequest);

            command.ApplicationId = Core.BaseEnums.MobileApplicationId.C3Pay;

            var verifyDetailsResult = await this._userCommandHandler.Handle(command);

            if (!verifyDetailsResult.IsSuccessful)
            {
                return this.BadRequest(verifyDetailsResult.ErrorMessage);
            }

            return this.Ok(verifyDetailsResult.Data);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="verifyForgotPasswordDetailsRequest"></param>
        /// <returns></returns>
        [HttpPost("forgot-password/verification/pin")]
        public async Task<ActionResult<Guid>> ForgotPasswordVerifyPin(VerifyLoggedOutPinRequestDto verifyForgotPasswordDetailsRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var verifyPhoneResult = await this._userCommandHandler.Handle(new CheckResetPasswordEligibilityCommand()
            {
                PhoneNumber = verifyForgotPasswordDetailsRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            var eligibility = verifyPhoneResult.Data.Eligibility;

            if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForForgotPassword)
            {
                if (eligibility != BaseEnums.ResetPasswordFlowEligibilityResult.EligibleForAutoUnblock)
                {
                    eligibility = BaseEnums.ResetPasswordFlowEligibilityResult.InEligible;
                }

                return this.BadRequest(eligibility.ToString());
            }

            var command = this._mapper.Map<VerifyUserPinCommand>(verifyForgotPasswordDetailsRequest);

            var verifyDetailsResult = await this._userCommandHandler.Handle(command);

            if (!verifyDetailsResult.IsSuccessful)
            {
                return this.BadRequest(verifyDetailsResult.ErrorMessage);
            }

            return this.Ok(VerifyAtmPinResult.PinVerified.ToString());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet("forgot-password/{userId}/security-questions")]
        public async Task<ActionResult<IEnumerable<SecurityQuestion>>> ForgotPasswordGetUserSecurityQuestions(Guid userId)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var getUserQuestionsResult = await this._userCommandHandler.Handle(new GetUserSecurityQuestionsCommand()
            {
                UserId = userId
            });

            if (!getUserQuestionsResult.IsSuccessful)
            {
                return BadRequest(getUserQuestionsResult.ErrorMessage);
            }

            return this.Ok(getUserQuestionsResult.Data);
        }

        /// <summary>
        /// Verifies secret answers. possible returns: 400-WrongQuestion, 400-WrongAnswer, 200-Verified
        /// </summary>
        /// <param name="verifySecretAnswerRequest"></param>
        /// <returns></returns>
        [HttpPost("forgot-password/verification/secret-answer")]
        public async Task<ActionResult> ForgotPasswordVerifyUserSecretAnswer(VerifyUserSecretAnswerRequestDto verifySecretAnswerRequest)
        {
            var isAuthorized = await IsUserOTPAuthorized();

            if (!isAuthorized)
            {
                return this.Unauthorized();
            }

            var validateSecretAnswerResult = await this._userCommandHandler.Handle(new VerifyUserSecretAnswerCommand()
            {
                SecretAnswer = verifySecretAnswerRequest.SecretAnswer,
                SecurityQuestionId = verifySecretAnswerRequest.SecurityQuestionId,
                UserId = verifySecretAnswerRequest.UserId
            });

            if (!validateSecretAnswerResult.IsSuccessful)
            {
                return BadRequest(validateSecretAnswerResult.ErrorMessage);
            }

            if (validateSecretAnswerResult.Data != BaseEnums.SecretAnswerVerificationResult.Verified)
            {
                return BadRequest(validateSecretAnswerResult.Data.ToString());
            }

            return this.Ok(validateSecretAnswerResult.Data.ToString());
        }

        /// <summary>
        /// Possible Returns 400-Error, 400-InvalidPhoneNumber, 400-UserBlocked, 200-UserId
        /// </summary>
        /// <param name="verifyPhoneNumberRequest"></param>
        /// <returns></returns>
        [HttpPost("forgot-password/verification/phone-number")]
        public async Task<ActionResult> ForgotPasswordVerifyPhoneNumber(VerifyPhoneNumberRequestDto verifyPhoneNumberRequest)
        {
            var verifyPhoneResult = await this._userCommandHandler.Handle(new GetUserIdByPhoneNumberCommand()
            {
                PhoneNumber = verifyPhoneNumberRequest.PhoneNumber
            });

            if (!verifyPhoneResult.IsSuccessful)
            {
                return this.BadRequest(verifyPhoneResult.ErrorMessage);
            }

            return this.Ok(verifyPhoneResult.Data);
        }
        #endregion

        private async Task<bool> IsUserOTPAuthorized()
        {
            string loggedOutTokenType = Request.Headers["verificationTokenType"];
            string loggedOutToken = Request.Headers["verificationToken"];
            string loggedOutTokenIdentifier = Request.Headers["verificationTokenIdentifier"];

            if (string.IsNullOrEmpty(loggedOutTokenType) || loggedOutTokenType != BaseEnums.LoggedOutTokenType.OTP.ToString() || string.IsNullOrEmpty(loggedOutToken) || string.IsNullOrEmpty(loggedOutTokenIdentifier) || loggedOutTokenIdentifier.Length != 14)
            {
                return false;
            }

            var partitionkey = loggedOutTokenIdentifier[..7];
            var rowKey = loggedOutTokenIdentifier.Substring(6, 7);
            var otpResult = await this._tableStorageService.GetEntityAsync(_otpStorageTableName, partitionkey, rowKey);
            var otpData = otpResult.Data;

            if (!otpResult.IsSuccessful || otpData is null)
            {
                return false;
            }

            return loggedOutToken == otpData.Value && otpData.Timestamp.UtcDateTime.AddMinutes(_generalSettings.CachedEntityExpiryInMinutes) > DateTime.UtcNow;
        }

        private async Task<bool> IsUserPasswordAuthorized(Guid userId)
        {
            string loggedOutTokenType = Request.Headers["verificationTokenType"];
            string loggedOutToken = Request.Headers["verificationToken"];

            if (string.IsNullOrEmpty(loggedOutTokenType) || string.IsNullOrEmpty(loggedOutToken) || loggedOutTokenType != BaseEnums.LoggedOutTokenType.PASSWORD.ToString())
            {
                return false;
            }

            var validatePasswordResult = await _userCommandHandler.Handle(new Services.Commands.VerifyUserPasswordCommand()
            {
                UserId = userId,
                Password = loggedOutToken
            });

            return validatePasswordResult.Data == Enums.PasswordValidationResult.ValidPassword;
        }

    }
}
