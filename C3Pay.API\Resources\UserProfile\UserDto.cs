﻿using C3Pay.Core;
using C3Pay.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace C3Pay.API.Resources.UserProfile
{
    /// <summary>
    /// 
    /// </summary>
    public class UserDto
    {

        /// <summary>
        /// 
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CorporateName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CorporateId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EmployeeId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CitizenId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [NotMapped]
        public string EmiratesId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Nationality { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DeviceToken { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RMTProfileStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EmiratesIdStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EmiratesIdExpiryDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string DateOfBirth { get; set; }

        public string PlaceOfBirth { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RegistrationDate { get; set; }

        /// <summary>
        /// Checks if the user did a pending or successful money transfer transaction
        /// </summary>
        public bool HasMoneyTransferTransactions { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool HasMoneyTransferBeneficiary { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool HasLocalMobileRechargeTransactions { get; set; }

        /// <summary>
        ///
        /// </summary>
        public bool HasInternationalMobileRechargeTransactions { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool RequiresPasswordReset { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int CardPlasticType { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool ATMPinPopupEnabled { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? ATMPinBlockEndDate { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MoneyTransferVideoURL { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string MoneyTransferVideoThumbnailURL { get; set; }

        public string BillPaymentVideoURL { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BillPaymentVideoThumbnailURL { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReferralProgramVideoURL { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReferralProgramVideoThumbnailURL { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ReferralCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool RewardedForReferral { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int MoneyTransferReferralCount { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PassportStatus { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsVerified { get; set; }

        public bool AutoplayMoneyTransferVideo { get; set; }

        public string LabourCardId { get; set; }

        public bool IsEligibleForBnpl { get; set; }

        public bool ShowDashboardNavigation { get; set; }

        public bool EligibleForUnemploymentInsurance { get; set; }
        public string DeviceId { get; set; }
        public decimal? MoneyTransferFees { get; set; }

        public bool IsMTStoriesEnabled { get; set; }
        /// <summary>
        /// User's MoneyBack Experiment Group Code
        /// </summary>
        public string MoneyTransferMoneyBackExperimentGroupCode { get; set; }

        /// <summary>
        /// Experiment data assigned to user
        /// </summary>
        public List<ExperimentGroupsDto> Experiments { get; set; }

        public string Role { get; set; }
        public string PartnerLogoUrl { get; set; }
        public string EhRatesAreBetterVideoURL { get; set; }
        public string EhRatesAreBetterThumbnailURL { get; set; }
        public string LoginVideoUrl { get; set; }

        /// <summary>
        /// Gets the Social Proofing data of the user
        /// </summary>
        public List<SocialProofingDto> SocialProofing { get; set; }

        /// <summary>
        /// Gets the count of money transfer transactions
        /// </summary>
        public int MoneyTransferTransactionsCount { get; set; }

        public int MTInternationalBeneficiaryCount { get; set; }

        /// <summary>
        /// List of Suspicious beneficiaries of the user
        /// </summary>
        public List<MoneyTransferSuspiciousBeneficiaryDto> SuspiciousBeneficiaries { get; set; }

        /// <summary>
        /// Gets the Rmt Profile Sent
        /// </summary>
        public DateTime? RmtProfileSentDateTime { get; set; }

        public DateTime? FreeTransferExpiryDate { get; set; }
        public DateTime? MtGoldIncentiveExpiryDate { get; set; }
        public bool IsFirstSalaryProcessed { get; set; }

        public List<UserInterfaceAction> UserInterfaceActions { get; set; }

        // Temporarily added this flag for SMS+ rollout plan to support Mobile APP
        public bool IsSmsPlusEnabled { get; set; }
        public bool EmailConsentGiven { get; set; }

        public bool? IsInternationalPhoneNumberUser { get; set; }

        public bool IsInAppAuthUser { get; set; }
    }

    public class UserInterfaceAction
    {
        public string Feature { get; set; }
        public string Flow { get; set; }
    }
}